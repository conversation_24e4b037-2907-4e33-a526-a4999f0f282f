<?php
/**
 * Base Test Case
 *
 * @package Anthropora\VlogPlugin\Tests
 */

namespace Anthropora\VlogPlugin\Tests;

use WP_UnitTestCase;

/**
 * Base test case for all plugin tests
 */
abstract class TestCase extends WP_UnitTestCase {

    /**
     * Plugin instance
     *
     * @var \Anthropora\VlogPlugin\Plugin
     */
    protected $plugin;

    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();
        
        // Initialize plugin
        $this->plugin = new \Anthropora\VlogPlugin\Plugin();
        $this->plugin->init();
        
        // Set up test data
        $this->setUpTestData();
    }

    /**
     * Tear down test environment
     */
    public function tearDown(): void {
        $this->cleanUpTestData();
        parent::tearDown();
    }

    /**
     * Set up test data
     */
    protected function setUpTestData(): void {
        // Create test options
        update_option('vlog_plugin_options', [
            'videos_per_page' => 12,
            'enable_youtube' => true,
            'enable_rumble' => true,
            'cache_duration' => 3600,
            'thumbnail_size' => 'medium',
            'enable_lightbox' => true,
            'show_video_duration' => true,
            'show_video_date' => true,
        ]);
    }

    /**
     * Clean up test data
     */
    protected function cleanUpTestData(): void {
        // Clean up posts
        $posts = get_posts([
            'post_type' => 'vlog',
            'numberposts' => -1,
            'post_status' => 'any',
        ]);

        foreach ($posts as $post) {
            wp_delete_post($post->ID, true);
        }

        // Clean up terms
        $terms = get_terms([
            'taxonomy' => ['vlog_category', 'vlog_tag'],
            'hide_empty' => false,
        ]);

        foreach ($terms as $term) {
            wp_delete_term($term->term_id, $term->taxonomy);
        }

        // Clean up options
        delete_option('vlog_plugin_options');
        delete_option('vlog_plugin_version');
        delete_option('vlog_plugin_main_page_id');

        // Clear cache
        wp_cache_flush();
    }

    /**
     * Create test vlog post
     *
     * @param array $args Post arguments
     * @return int Post ID
     */
    protected function createTestVlog($args = []): int {
        $defaults = [
            'post_title' => 'Test Video',
            'post_content' => 'Test video description',
            'post_status' => 'publish',
            'post_type' => 'vlog',
        ];

        $args = wp_parse_args($args, $defaults);
        $post_id = wp_insert_post($args);

        // Add video meta
        update_post_meta($post_id, '_vlog_video_url', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ');
        update_post_meta($post_id, '_vlog_video_id', 'dQw4w9WgXcQ');
        update_post_meta($post_id, '_vlog_platform', 'youtube');
        update_post_meta($post_id, '_vlog_duration', 212);
        update_post_meta($post_id, '_vlog_embed_url', 'https://www.youtube.com/embed/dQw4w9WgXcQ');
        update_post_meta($post_id, '_vlog_thumbnail_url', 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg');
        update_post_meta($post_id, '_vlog_author', 'Test Author');

        return $post_id;
    }

    /**
     * Create test category
     *
     * @param array $args Category arguments
     * @return int Term ID
     */
    protected function createTestCategory($args = []): int {
        $defaults = [
            'name' => 'Test Category',
            'slug' => 'test-category',
        ];

        $args = wp_parse_args($args, $defaults);
        $term = wp_insert_term($args['name'], 'vlog_category', $args);

        return $term['term_id'];
    }

    /**
     * Assert that a post is a valid vlog
     *
     * @param int $post_id Post ID
     */
    protected function assertIsValidVlog($post_id): void {
        $post = get_post($post_id);
        
        $this->assertNotNull($post);
        $this->assertEquals('vlog', $post->post_type);
        
        // Check required meta
        $this->assertNotEmpty(get_post_meta($post_id, '_vlog_video_url', true));
        $this->assertNotEmpty(get_post_meta($post_id, '_vlog_platform', true));
    }

    /**
     * Assert that video data is valid
     *
     * @param array $video_data Video data
     */
    protected function assertValidVideoData($video_data): void {
        $this->assertIsArray($video_data);
        $this->assertArrayHasKey('title', $video_data);
        $this->assertArrayHasKey('video_id', $video_data);
        $this->assertArrayHasKey('platform', $video_data);
        $this->assertArrayHasKey('embed_url', $video_data);
        
        $this->assertNotEmpty($video_data['title']);
        $this->assertNotEmpty($video_data['video_id']);
        $this->assertNotEmpty($video_data['platform']);
        $this->assertNotEmpty($video_data['embed_url']);
    }

    /**
     * Mock HTTP request
     *
     * @param string $url URL to mock
     * @param array  $response Response data
     */
    protected function mockHttpRequest($url, $response): void {
        add_filter('pre_http_request', function($preempt, $args, $url_param) use ($url, $response) {
            if ($url_param === $url) {
                return $response;
            }
            return $preempt;
        }, 10, 3);
    }

    /**
     * Get sample YouTube response
     *
     * @return array Sample response
     */
    protected function getSampleYouTubeResponse(): array {
        return [
            'response' => ['code' => 200],
            'body' => json_encode([
                'items' => [
                    [
                        'snippet' => [
                            'title' => 'Test Video Title',
                            'description' => 'Test video description',
                            'channelTitle' => 'Test Channel',
                            'publishedAt' => '2023-01-01T00:00:00Z',
                            'thumbnails' => [
                                'high' => [
                                    'url' => 'https://img.youtube.com/vi/test/maxresdefault.jpg'
                                ]
                            ]
                        ],
                        'contentDetails' => [
                            'duration' => 'PT3M32S'
                        ],
                        'statistics' => [
                            'viewCount' => '1000'
                        ]
                    ]
                ]
            ])
        ];
    }

    /**
     * Get sample Rumble HTML response
     *
     * @return array Sample response
     */
    protected function getSampleRumbleResponse(): array {
        return [
            'response' => ['code' => 200],
            'body' => '<html><head><title>Test Video - Rumble</title><meta property="og:title" content="Test Video"><meta property="og:description" content="Test description"><meta property="og:image" content="https://example.com/thumb.jpg"></head></html>'
        ];
    }

    /**
     * Assert HTML contains expected elements
     *
     * @param string $html HTML content
     * @param array  $expected Expected elements
     */
    protected function assertHtmlContains($html, $expected): void {
        foreach ($expected as $element) {
            $this->assertStringContainsString($element, $html);
        }
    }

    /**
     * Assert HTML does not contain elements
     *
     * @param string $html HTML content
     * @param array  $not_expected Elements that should not be present
     */
    protected function assertHtmlNotContains($html, $not_expected): void {
        foreach ($not_expected as $element) {
            $this->assertStringNotContainsString($element, $html);
        }
    }
}
