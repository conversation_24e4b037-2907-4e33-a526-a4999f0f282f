<?php
/**
 * YouTube Video Parser
 *
 * @package Anthropora\VlogPlugin\API\Parsers
 */

namespace Anthropora\VlogPlugin\API\Parsers;

/**
 * YouTube video parser
 */
class YouTubeParser implements ParserInterface {

    /**
     * YouTube API key (set in plugin settings)
     *
     * @var string
     */
    private $api_key;

    /**
     * Constructor
     */
    public function __construct() {
        $options = get_option('vlog_plugin_options', []);
        $this->api_key = $options['youtube_api_key'] ?? '';
    }

    /**
     * Check if parser can handle the given URL
     *
     * @param string $url Video URL
     * @return bool
     */
    public function can_parse(string $url): bool {
        return (bool) preg_match('/(?:youtube\.com|youtu\.be)/', $url);
    }

    /**
     * Parse video URL and extract metadata
     *
     * @param string $url Video URL
     * @return array|false Video data or false on failure
     */
    public function parse(string $url) {
        $video_id = $this->get_video_id($url);
        
        if (!$video_id) {
            return false;
        }

        // Try API first, fallback to oEmbed
        if (!empty($this->api_key)) {
            $data = $this->parse_with_api($video_id);
            if ($data) {
                return $data;
            }
        }

        return $this->parse_with_oembed($url);
    }

    /**
     * Extract video ID from URL
     *
     * @param string $url Video URL
     * @return string|false Video ID or false on failure
     */
    public function get_video_id(string $url) {
        $patterns = [
            '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/',
            '/youtube\.com\/v\/([a-zA-Z0-9_-]{11})/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return $matches[1];
            }
        }

        return false;
    }

    /**
     * Get embed URL for video
     *
     * @param string $video_id Video ID
     * @return string Embed URL
     */
    public function get_embed_url(string $video_id): string {
        return "https://www.youtube.com/embed/{$video_id}";
    }

    /**
     * Parse using YouTube Data API
     *
     * @param string $video_id Video ID
     * @return array|false Video data or false on failure
     */
    private function parse_with_api(string $video_id) {
        $cache_key = "vlog_plugin_youtube_{$video_id}";
        $cached_data = get_transient($cache_key);
        
        if ($cached_data !== false) {
            return $cached_data;
        }

        $api_url = add_query_arg([
            'part' => 'snippet,contentDetails,statistics',
            'id' => $video_id,
            'key' => $this->api_key,
        ], 'https://www.googleapis.com/youtube/v3/videos');

        $response = wp_remote_get($api_url, [
            'timeout' => 30,
            'headers' => [
                'User-Agent' => 'WordPress Vlog Plugin',
            ],
        ]);

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (empty($data['items'][0])) {
            return false;
        }

        $video = $data['items'][0];
        $snippet = $video['snippet'];
        $content_details = $video['contentDetails'];
        $statistics = $video['statistics'];

        $parsed_data = [
            'video_id' => $video_id,
            'title' => $snippet['title'],
            'description' => $snippet['description'],
            'thumbnail' => $snippet['thumbnails']['high']['url'] ?? $snippet['thumbnails']['default']['url'],
            'duration' => $this->parse_duration($content_details['duration']),
            'embed_url' => $this->get_embed_url($video_id),
            'author' => $snippet['channelTitle'],
            'published_at' => $snippet['publishedAt'],
            'view_count' => (int) ($statistics['viewCount'] ?? 0),
        ];

        // Cache for 1 hour
        set_transient($cache_key, $parsed_data, HOUR_IN_SECONDS);

        return $parsed_data;
    }

    /**
     * Parse using oEmbed (fallback)
     *
     * @param string $url Video URL
     * @return array|false Video data or false on failure
     */
    private function parse_with_oembed(string $url) {
        $video_id = $this->get_video_id($url);
        
        if (!$video_id) {
            return false;
        }

        $oembed_url = add_query_arg([
            'url' => $url,
            'format' => 'json',
        ], 'https://www.youtube.com/oembed');

        $response = wp_remote_get($oembed_url, ['timeout' => 30]);

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (empty($data['title'])) {
            return false;
        }

        return [
            'video_id' => $video_id,
            'title' => $data['title'],
            'description' => '',
            'thumbnail' => $data['thumbnail_url'] ?? '',
            'duration' => 0,
            'embed_url' => $this->get_embed_url($video_id),
            'author' => $data['author_name'] ?? '',
            'published_at' => '',
            'view_count' => 0,
        ];
    }

    /**
     * Parse ISO 8601 duration to seconds
     *
     * @param string $duration ISO 8601 duration (e.g., PT4M13S)
     * @return int Duration in seconds
     */
    private function parse_duration(string $duration): int {
        $interval = new \DateInterval($duration);
        return ($interval->h * 3600) + ($interval->i * 60) + $interval->s;
    }
}
