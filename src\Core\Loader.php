<?php
/**
 * Hook Loader Class
 *
 * @package Anthropora\VlogPlugin\Core
 */

namespace Anthropora\VlogPlugin\Core;

/**
 * Hook loader for organizing WordPress hooks
 */
class Loader {

    /**
     * Array of actions registered with WordPress
     *
     * @var array
     */
    protected $actions = [];

    /**
     * Array of filters registered with WordPress
     *
     * @var array
     */
    protected $filters = [];

    /**
     * Add a new action to the collection
     *
     * @param string $hook          Hook name
     * @param object $component     Object instance
     * @param string $callback      Method name
     * @param int    $priority      Hook priority
     * @param int    $accepted_args Number of accepted arguments
     */
    public function add_action(string $hook, $component, string $callback, int $priority = 10, int $accepted_args = 1): void {
        $this->actions = $this->add($this->actions, $hook, $component, $callback, $priority, $accepted_args);
    }

    /**
     * Add a new filter to the collection
     *
     * @param string $hook          Hook name
     * @param object $component     Object instance
     * @param string $callback      Method name
     * @param int    $priority      Hook priority
     * @param int    $accepted_args Number of accepted arguments
     */
    public function add_filter(string $hook, $component, string $callback, int $priority = 10, int $accepted_args = 1): void {
        $this->filters = $this->add($this->filters, $hook, $component, $callback, $priority, $accepted_args);
    }

    /**
     * Add hook to collection
     *
     * @param array  $hooks         Hook collection
     * @param string $hook          Hook name
     * @param object $component     Object instance
     * @param string $callback      Method name
     * @param int    $priority      Hook priority
     * @param int    $accepted_args Number of accepted arguments
     * @return array
     */
    private function add(array $hooks, string $hook, $component, string $callback, int $priority, int $accepted_args): array {
        $hooks[] = [
            'hook'          => $hook,
            'component'     => $component,
            'callback'      => $callback,
            'priority'      => $priority,
            'accepted_args' => $accepted_args,
        ];

        return $hooks;
    }

    /**
     * Register all hooks with WordPress
     */
    public function run(): void {
        foreach ($this->filters as $hook) {
            add_filter(
                $hook['hook'],
                [$hook['component'], $hook['callback']],
                $hook['priority'],
                $hook['accepted_args']
            );
        }

        foreach ($this->actions as $hook) {
            add_action(
                $hook['hook'],
                [$hook['component'], $hook['callback']],
                $hook['priority'],
                $hook['accepted_args']
            );
        }
    }
}
