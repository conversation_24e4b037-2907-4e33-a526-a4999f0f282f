<?php
/**
 * Settings Page
 *
 * @package Anthropora\VlogPlugin\Admin
 */

namespace Anthropora\VlogPlugin\Admin;

/**
 * Handles plugin settings page
 */
class SettingsPage {

    /**
     * Settings group name
     */
    const SETTINGS_GROUP = 'vlog_plugin_settings';

    /**
     * Option name
     */
    const OPTION_NAME = 'vlog_plugin_options';

    /**
     * Initialize settings page
     */
    public function init(): void {
        add_action('admin_init', [$this, 'register_settings']);
    }

    /**
     * Register settings
     */
    public function register_settings(): void {
        register_setting(
            self::SETTINGS_GROUP,
            self::OPTION_NAME,
            [$this, 'sanitize_settings']
        );

        // General settings section
        add_settings_section(
            'vlog_general_settings',
            __('General Settings', 'vlog-plugin'),
            [$this, 'render_general_section'],
            'vlog-settings'
        );

        // API settings section
        add_settings_section(
            'vlog_api_settings',
            __('API Settings', 'vlog-plugin'),
            [$this, 'render_api_section'],
            'vlog-settings'
        );

        // Display settings section
        add_settings_section(
            'vlog_display_settings',
            __('Display Settings', 'vlog-plugin'),
            [$this, 'render_display_section'],
            'vlog-settings'
        );

        // Add settings fields
        $this->add_settings_fields();
    }

    /**
     * Add settings fields
     */
    private function add_settings_fields(): void {
        $options = get_option(self::OPTION_NAME, []);

        // General settings
        add_settings_field(
            'videos_per_page',
            __('Videos per Page', 'vlog-plugin'),
            [$this, 'render_number_field'],
            'vlog-settings',
            'vlog_general_settings',
            [
                'field' => 'videos_per_page',
                'value' => $options['videos_per_page'] ?? 12,
                'min' => 1,
                'max' => 100,
                'description' => __('Number of videos to display per page in galleries.', 'vlog-plugin'),
            ]
        );

        add_settings_field(
            'cache_duration',
            __('Cache Duration (seconds)', 'vlog-plugin'),
            [$this, 'render_number_field'],
            'vlog-settings',
            'vlog_general_settings',
            [
                'field' => 'cache_duration',
                'value' => $options['cache_duration'] ?? 3600,
                'min' => 300,
                'max' => 86400,
                'description' => __('How long to cache video data (300-86400 seconds).', 'vlog-plugin'),
            ]
        );

        // API settings
        add_settings_field(
            'youtube_api_key',
            __('YouTube API Key', 'vlog-plugin'),
            [$this, 'render_text_field'],
            'vlog-settings',
            'vlog_api_settings',
            [
                'field' => 'youtube_api_key',
                'value' => $options['youtube_api_key'] ?? '',
                'type' => 'password',
                'description' => __('YouTube Data API v3 key for enhanced video parsing.', 'vlog-plugin'),
            ]
        );

        add_settings_field(
            'enable_youtube',
            __('Enable YouTube', 'vlog-plugin'),
            [$this, 'render_checkbox_field'],
            'vlog-settings',
            'vlog_api_settings',
            [
                'field' => 'enable_youtube',
                'value' => $options['enable_youtube'] ?? true,
                'description' => __('Allow parsing YouTube videos.', 'vlog-plugin'),
            ]
        );

        add_settings_field(
            'enable_rumble',
            __('Enable Rumble', 'vlog-plugin'),
            [$this, 'render_checkbox_field'],
            'vlog-settings',
            'vlog_api_settings',
            [
                'field' => 'enable_rumble',
                'value' => $options['enable_rumble'] ?? true,
                'description' => __('Allow parsing Rumble videos.', 'vlog-plugin'),
            ]
        );

        // Display settings
        add_settings_field(
            'thumbnail_size',
            __('Thumbnail Size', 'vlog-plugin'),
            [$this, 'render_select_field'],
            'vlog-settings',
            'vlog_display_settings',
            [
                'field' => 'thumbnail_size',
                'value' => $options['thumbnail_size'] ?? 'medium',
                'options' => [
                    'small' => __('Small', 'vlog-plugin'),
                    'medium' => __('Medium', 'vlog-plugin'),
                    'large' => __('Large', 'vlog-plugin'),
                ],
                'description' => __('Default thumbnail size for video galleries.', 'vlog-plugin'),
            ]
        );

        add_settings_field(
            'enable_lightbox',
            __('Enable Lightbox', 'vlog-plugin'),
            [$this, 'render_checkbox_field'],
            'vlog-settings',
            'vlog_display_settings',
            [
                'field' => 'enable_lightbox',
                'value' => $options['enable_lightbox'] ?? true,
                'description' => __('Open videos in a lightbox overlay.', 'vlog-plugin'),
            ]
        );

        add_settings_field(
            'show_video_duration',
            __('Show Video Duration', 'vlog-plugin'),
            [$this, 'render_checkbox_field'],
            'vlog-settings',
            'vlog_display_settings',
            [
                'field' => 'show_video_duration',
                'value' => $options['show_video_duration'] ?? true,
                'description' => __('Display video duration on thumbnails.', 'vlog-plugin'),
            ]
        );

        add_settings_field(
            'show_video_date',
            __('Show Video Date', 'vlog-plugin'),
            [$this, 'render_checkbox_field'],
            'vlog-settings',
            'vlog_display_settings',
            [
                'field' => 'show_video_date',
                'value' => $options['show_video_date'] ?? true,
                'description' => __('Display video publication date.', 'vlog-plugin'),
            ]
        );
    }

    /**
     * Render settings page
     */
    public function render_page(): void {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'vlog-plugin'));
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <form method="post" action="options.php">
                <?php
                settings_fields(self::SETTINGS_GROUP);
                do_settings_sections('vlog-settings');
                submit_button();
                ?>
            </form>
            
            <div class="vlog-settings-sidebar">
                <div class="postbox">
                    <h3 class="hndle"><?php esc_html_e('Quick Actions', 'vlog-plugin'); ?></h3>
                    <div class="inside">
                        <p>
                            <a href="<?php echo admin_url('edit.php?post_type=vlog&page=vlog-import'); ?>" class="button">
                                <?php esc_html_e('Import Videos', 'vlog-plugin'); ?>
                            </a>
                        </p>
                        <p>
                            <button type="button" id="clear-cache" class="button">
                                <?php esc_html_e('Clear Cache', 'vlog-plugin'); ?>
                            </button>
                        </p>
                    </div>
                </div>
                
                <div class="postbox">
                    <h3 class="hndle"><?php esc_html_e('System Info', 'vlog-plugin'); ?></h3>
                    <div class="inside">
                        <p><strong><?php esc_html_e('Plugin Version:', 'vlog-plugin'); ?></strong> <?php echo VLOG_PLUGIN_VERSION; ?></p>
                        <p><strong><?php esc_html_e('WordPress Version:', 'vlog-plugin'); ?></strong> <?php echo get_bloginfo('version'); ?></p>
                        <p><strong><?php esc_html_e('PHP Version:', 'vlog-plugin'); ?></strong> <?php echo PHP_VERSION; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#clear-cache').on('click', function() {
                if (confirm('<?php esc_js_e('Are you sure you want to clear all cached video data?', 'vlog-plugin'); ?>')) {
                    $.post(ajaxurl, {
                        action: 'vlog_clear_cache',
                        nonce: '<?php echo wp_create_nonce('vlog_clear_cache_nonce'); ?>'
                    }, function(response) {
                        if (response.success) {
                            alert('<?php esc_js_e('Cache cleared successfully!', 'vlog-plugin'); ?>');
                        } else {
                            alert('<?php esc_js_e('Error clearing cache.', 'vlog-plugin'); ?>');
                        }
                    });
                }
            });
        });
        </script>

        <style>
        .vlog-settings-sidebar {
            float: right;
            width: 300px;
            margin-left: 20px;
        }
        .wrap form {
            margin-right: 320px;
        }
        </style>
        <?php
    }

    /**
     * Render general section
     */
    public function render_general_section(): void {
        echo '<p>' . esc_html__('Configure general plugin settings.', 'vlog-plugin') . '</p>';
    }

    /**
     * Render API section
     */
    public function render_api_section(): void {
        echo '<p>' . esc_html__('Configure API settings for video platforms.', 'vlog-plugin') . '</p>';
    }

    /**
     * Render display section
     */
    public function render_display_section(): void {
        echo '<p>' . esc_html__('Configure how videos are displayed on your site.', 'vlog-plugin') . '</p>';
    }

    /**
     * Render text field
     *
     * @param array $args Field arguments
     */
    public function render_text_field($args): void {
        $field = $args['field'];
        $value = $args['value'];
        $type = $args['type'] ?? 'text';
        $description = $args['description'] ?? '';

        printf(
            '<input type="%s" id="%s" name="%s[%s]" value="%s" class="regular-text" />',
            esc_attr($type),
            esc_attr($field),
            esc_attr(self::OPTION_NAME),
            esc_attr($field),
            esc_attr($value)
        );

        if ($description) {
            printf('<p class="description">%s</p>', esc_html($description));
        }
    }

    /**
     * Render number field
     *
     * @param array $args Field arguments
     */
    public function render_number_field($args): void {
        $field = $args['field'];
        $value = $args['value'];
        $min = $args['min'] ?? '';
        $max = $args['max'] ?? '';
        $description = $args['description'] ?? '';

        printf(
            '<input type="number" id="%s" name="%s[%s]" value="%s" class="small-text" min="%s" max="%s" />',
            esc_attr($field),
            esc_attr(self::OPTION_NAME),
            esc_attr($field),
            esc_attr($value),
            esc_attr($min),
            esc_attr($max)
        );

        if ($description) {
            printf('<p class="description">%s</p>', esc_html($description));
        }
    }

    /**
     * Render checkbox field
     *
     * @param array $args Field arguments
     */
    public function render_checkbox_field($args): void {
        $field = $args['field'];
        $value = $args['value'];
        $description = $args['description'] ?? '';

        printf(
            '<input type="checkbox" id="%s" name="%s[%s]" value="1" %s />',
            esc_attr($field),
            esc_attr(self::OPTION_NAME),
            esc_attr($field),
            checked($value, true, false)
        );

        if ($description) {
            printf('<label for="%s"> %s</label>', esc_attr($field), esc_html($description));
        }
    }

    /**
     * Render select field
     *
     * @param array $args Field arguments
     */
    public function render_select_field($args): void {
        $field = $args['field'];
        $value = $args['value'];
        $options = $args['options'];
        $description = $args['description'] ?? '';

        printf('<select id="%s" name="%s[%s]">', esc_attr($field), esc_attr(self::OPTION_NAME), esc_attr($field));
        
        foreach ($options as $option_value => $option_label) {
            printf(
                '<option value="%s" %s>%s</option>',
                esc_attr($option_value),
                selected($value, $option_value, false),
                esc_html($option_label)
            );
        }
        
        echo '</select>';

        if ($description) {
            printf('<p class="description">%s</p>', esc_html($description));
        }
    }

    /**
     * Sanitize settings
     *
     * @param array $input Raw input data
     * @return array Sanitized data
     */
    public function sanitize_settings($input): array {
        $sanitized = [];

        // Sanitize each field
        $sanitized['videos_per_page'] = absint($input['videos_per_page'] ?? 12);
        $sanitized['cache_duration'] = absint($input['cache_duration'] ?? 3600);
        $sanitized['youtube_api_key'] = sanitize_text_field($input['youtube_api_key'] ?? '');
        $sanitized['enable_youtube'] = !empty($input['enable_youtube']);
        $sanitized['enable_rumble'] = !empty($input['enable_rumble']);
        $sanitized['thumbnail_size'] = sanitize_text_field($input['thumbnail_size'] ?? 'medium');
        $sanitized['enable_lightbox'] = !empty($input['enable_lightbox']);
        $sanitized['show_video_duration'] = !empty($input['show_video_duration']);
        $sanitized['show_video_date'] = !empty($input['show_video_date']);

        // Validate ranges
        $sanitized['videos_per_page'] = max(1, min(100, $sanitized['videos_per_page']));
        $sanitized['cache_duration'] = max(300, min(86400, $sanitized['cache_duration']));

        return $sanitized;
    }
}
