<?php
/**
 * Video URL Parser
 *
 * @package Anthropora\VlogPlugin\API
 */

namespace Anthropora\VlogPlugin\API;

use Anthropora\VlogPlugin\API\Parsers\YouTubeParser;
use Anthropora\VlogPlugin\API\Parsers\RumbleParser;

/**
 * Main video parser class
 */
class VideoParser {

    /**
     * Supported video platforms
     *
     * @var array
     */
    private $parsers = [];

    /**
     * Constructor
     */
    public function __construct() {
        $this->parsers = [
            'youtube' => new YouTubeParser(),
            'rumble'  => new RumbleParser(),
        ];
    }

    /**
     * Parse video URL and extract metadata
     *
     * @param string $url Video URL
     * @return array|false Video data or false on failure
     */
    public function parse_video_url(string $url) {
        $url = trim($url);
        
        if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        // Try each parser
        foreach ($this->parsers as $platform => $parser) {
            if ($parser->can_parse($url)) {
                $video_data = $parser->parse($url);
                
                if ($video_data) {
                    $video_data['platform'] = $platform;
                    $video_data['original_url'] = $url;
                    $video_data['parsed_at'] = current_time('mysql');
                    
                    return $this->sanitize_video_data($video_data);
                }
            }
        }

        return false;
    }

    /**
     * Get video ID from URL
     *
     * @param string $url Video URL
     * @return string|false Video ID or false on failure
     */
    public function get_video_id(string $url) {
        foreach ($this->parsers as $parser) {
            if ($parser->can_parse($url)) {
                return $parser->get_video_id($url);
            }
        }

        return false;
    }

    /**
     * Check if URL is supported
     *
     * @param string $url Video URL
     * @return bool
     */
    public function is_supported_url(string $url): bool {
        foreach ($this->parsers as $parser) {
            if ($parser->can_parse($url)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get supported platforms
     *
     * @return array
     */
    public function get_supported_platforms(): array {
        return array_keys($this->parsers);
    }

    /**
     * Sanitize video data
     *
     * @param array $data Raw video data
     * @return array Sanitized data
     */
    private function sanitize_video_data(array $data): array {
        return [
            'title'       => sanitize_text_field($data['title'] ?? ''),
            'description' => wp_kses_post($data['description'] ?? ''),
            'thumbnail'   => esc_url_raw($data['thumbnail'] ?? ''),
            'duration'    => absint($data['duration'] ?? 0),
            'video_id'    => sanitize_text_field($data['video_id'] ?? ''),
            'embed_url'   => esc_url_raw($data['embed_url'] ?? ''),
            'platform'    => sanitize_text_field($data['platform'] ?? ''),
            'original_url'=> esc_url_raw($data['original_url'] ?? ''),
            'parsed_at'   => sanitize_text_field($data['parsed_at'] ?? ''),
            'author'      => sanitize_text_field($data['author'] ?? ''),
            'published_at'=> sanitize_text_field($data['published_at'] ?? ''),
            'view_count'  => absint($data['view_count'] ?? 0),
        ];
    }

    /**
     * Batch parse multiple URLs
     *
     * @param array $urls Array of video URLs
     * @return array Array of parsed video data
     */
    public function batch_parse(array $urls): array {
        $results = [];
        
        foreach ($urls as $url) {
            $parsed = $this->parse_video_url($url);
            if ($parsed) {
                $results[] = $parsed;
            }
        }

        return $results;
    }
}
