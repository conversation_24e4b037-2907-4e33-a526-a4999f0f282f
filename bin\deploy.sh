#!/bin/bash

# Professional Vlog Plugin Deployment Script
# This script prepares the plugin for deployment to WordPress.org repository

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PLUGIN_SLUG="vlog-plugin"
PLUGIN_NAME="Professional Vlog Plugin"
PLUGIN_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BUILD_DIR="$PLUGIN_DIR/build"
DIST_DIR="$PLUGIN_DIR/dist"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    log_info "Checking requirements..."
    
    if ! command -v composer &> /dev/null; then
        log_error "Composer is required but not installed."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm is required but not installed."
        exit 1
    fi
    
    if ! command -v zip &> /dev/null; then
        log_error "zip is required but not installed."
        exit 1
    fi
    
    log_success "All requirements met."
}

# Clean previous builds
clean_build() {
    log_info "Cleaning previous builds..."
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    if [ -d "$DIST_DIR" ]; then
        rm -rf "$DIST_DIR"
    fi
    
    mkdir -p "$BUILD_DIR"
    mkdir -p "$DIST_DIR"
    
    log_success "Build directories cleaned."
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    
    cd "$PLUGIN_DIR"
    
    # Install Composer dependencies (production only)
    composer install --no-dev --optimize-autoloader --no-interaction
    
    # Install npm dependencies
    npm ci --production
    
    log_success "Dependencies installed."
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    cd "$PLUGIN_DIR"
    
    # Install dev dependencies for testing
    composer install --dev --no-interaction
    
    # Run PHP tests
    if [ -f "phpunit.xml" ]; then
        ./vendor/bin/phpunit
    else
        log_warning "No PHPUnit configuration found, skipping PHP tests."
    fi
    
    # Run JavaScript tests
    if [ -f "package.json" ] && npm run test --if-present; then
        log_success "JavaScript tests passed."
    else
        log_warning "No JavaScript tests found or tests failed."
    fi
    
    # Remove dev dependencies
    composer install --no-dev --optimize-autoloader --no-interaction
    
    log_success "Tests completed."
}

# Run code quality checks
run_quality_checks() {
    log_info "Running code quality checks..."
    
    cd "$PLUGIN_DIR"
    
    # Install dev dependencies for quality checks
    composer install --dev --no-interaction
    
    # PHP CodeSniffer
    if [ -f "phpcs.xml" ]; then
        ./vendor/bin/phpcs --report=summary
        log_success "PHPCS checks passed."
    else
        log_warning "No PHPCS configuration found."
    fi
    
    # PHPStan
    if [ -f "phpstan.neon" ]; then
        ./vendor/bin/phpstan analyse --no-progress
        log_success "PHPStan analysis passed."
    else
        log_warning "No PHPStan configuration found."
    fi
    
    # Remove dev dependencies
    composer install --no-dev --optimize-autoloader --no-interaction
    
    log_success "Code quality checks completed."
}

# Build assets
build_assets() {
    log_info "Building assets..."
    
    cd "$PLUGIN_DIR"
    
    # Build CSS and JavaScript
    if [ -f "package.json" ]; then
        npm run build
        log_success "Assets built successfully."
    else
        log_warning "No package.json found, skipping asset build."
    fi
}

# Copy files to build directory
copy_files() {
    log_info "Copying files to build directory..."
    
    cd "$PLUGIN_DIR"
    
    # Files and directories to include
    include_files=(
        "vlog-plugin.php"
        "src/"
        "assets/"
        "templates/"
        "languages/"
        "vendor/"
        "readme.txt"
        "LICENSE"
    )
    
    # Copy included files
    for item in "${include_files[@]}"; do
        if [ -e "$item" ]; then
            cp -r "$item" "$BUILD_DIR/"
            log_info "Copied: $item"
        else
            log_warning "File not found: $item"
        fi
    done
    
    log_success "Files copied to build directory."
}

# Generate language files
generate_languages() {
    log_info "Generating language files..."
    
    cd "$BUILD_DIR"
    
    # Generate POT file if wp-cli is available
    if command -v wp &> /dev/null; then
        wp i18n make-pot . languages/vlog-plugin.pot --domain=vlog-plugin
        log_success "POT file generated."
    else
        log_warning "WP-CLI not found, skipping POT file generation."
    fi
}

# Validate plugin structure
validate_plugin() {
    log_info "Validating plugin structure..."
    
    cd "$BUILD_DIR"
    
    # Check required files
    required_files=(
        "vlog-plugin.php"
        "readme.txt"
        "src/Plugin.php"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "Required file missing: $file"
            exit 1
        fi
    done
    
    # Check plugin header
    if ! grep -q "Plugin Name:" vlog-plugin.php; then
        log_error "Plugin header missing in main file."
        exit 1
    fi
    
    # Check readme.txt format
    if ! grep -q "=== " readme.txt; then
        log_error "Invalid readme.txt format."
        exit 1
    fi
    
    log_success "Plugin structure validated."
}

# Create distribution package
create_package() {
    log_info "Creating distribution package..."
    
    cd "$BUILD_DIR/.."
    
    # Get version from plugin file
    VERSION=$(grep "Version:" "$BUILD_DIR/vlog-plugin.php" | sed 's/.*Version: *\([0-9.]*\).*/\1/')
    
    if [ -z "$VERSION" ]; then
        log_error "Could not determine plugin version."
        exit 1
    fi
    
    # Create zip file
    PACKAGE_NAME="$PLUGIN_SLUG-$VERSION.zip"
    cd "$BUILD_DIR"
    zip -r "$DIST_DIR/$PACKAGE_NAME" . -x "*.DS_Store" "*.git*" "node_modules/*" "tests/*"
    
    # Create latest version link
    cd "$DIST_DIR"
    ln -sf "$PACKAGE_NAME" "$PLUGIN_SLUG-latest.zip"
    
    log_success "Package created: $PACKAGE_NAME"
    log_info "Package size: $(du -h "$PACKAGE_NAME" | cut -f1)"
}

# Generate checksums
generate_checksums() {
    log_info "Generating checksums..."
    
    cd "$DIST_DIR"
    
    # Generate MD5 and SHA256 checksums
    for file in *.zip; do
        if [ -f "$file" ] && [ "$file" != "*" ]; then
            md5sum "$file" > "$file.md5"
            sha256sum "$file" > "$file.sha256"
            log_info "Checksums generated for: $file"
        fi
    done
    
    log_success "Checksums generated."
}

# Main deployment process
main() {
    log_info "Starting deployment process for $PLUGIN_NAME..."
    
    # Parse command line arguments
    SKIP_TESTS=false
    SKIP_QUALITY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --skip-quality)
                SKIP_QUALITY=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --skip-tests     Skip running tests"
                echo "  --skip-quality   Skip code quality checks"
                echo "  --help          Show this help message"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run deployment steps
    check_requirements
    clean_build
    install_dependencies
    
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    else
        log_warning "Skipping tests as requested."
    fi
    
    if [ "$SKIP_QUALITY" = false ]; then
        run_quality_checks
    else
        log_warning "Skipping quality checks as requested."
    fi
    
    build_assets
    copy_files
    generate_languages
    validate_plugin
    create_package
    generate_checksums
    
    log_success "Deployment completed successfully!"
    log_info "Distribution files available in: $DIST_DIR"
    
    # Show final summary
    echo ""
    echo "=== Deployment Summary ==="
    echo "Plugin: $PLUGIN_NAME"
    echo "Version: $(grep "Version:" "$BUILD_DIR/vlog-plugin.php" | sed 's/.*Version: *\([0-9.]*\).*/\1/')"
    echo "Build Directory: $BUILD_DIR"
    echo "Distribution Directory: $DIST_DIR"
    echo "Package Files:"
    ls -la "$DIST_DIR"/*.zip 2>/dev/null || echo "No packages found"
    echo "=========================="
}

# Run main function
main "$@"
