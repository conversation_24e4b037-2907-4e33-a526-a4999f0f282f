<?php
/**
 * Import Page Template
 *
 * @package Anthropora\VlogPlugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['import_videos']) && wp_verify_nonce($_POST['import_nonce'], 'vlog_import_videos')) {
    $urls = array_filter(array_map('trim', explode("\n", $_POST['video_urls'])));
    $category_id = intval($_POST['video_category']);
    $import_results = [];
    
    foreach ($urls as $url) {
        if (empty($url)) continue;
        
        $video_data = $this->video_parser->parse_video_url($url);
        
        if ($video_data) {
            // Create post
            $post_data = [
                'post_title' => $video_data['title'],
                'post_content' => $video_data['description'],
                'post_status' => 'publish',
                'post_type' => 'vlog',
                'post_author' => get_current_user_id(),
            ];
            
            $post_id = wp_insert_post($post_data);
            
            if ($post_id && !is_wp_error($post_id)) {
                // Save video meta
                foreach ($video_data as $key => $value) {
                    update_post_meta($post_id, '_vlog_' . $key, $value);
                }
                
                // Assign category
                if ($category_id) {
                    wp_set_post_terms($post_id, [$category_id], 'vlog_category');
                }
                
                $import_results[] = [
                    'success' => true,
                    'url' => $url,
                    'title' => $video_data['title'],
                    'post_id' => $post_id,
                ];
            } else {
                $import_results[] = [
                    'success' => false,
                    'url' => $url,
                    'error' => 'Failed to create post',
                ];
            }
        } else {
            $import_results[] = [
                'success' => false,
                'url' => $url,
                'error' => 'Failed to parse video URL',
            ];
        }
    }
}
?>

<div class="wrap">
    <h1><?php esc_html_e('Import Videos', 'vlog-plugin'); ?></h1>
    
    <?php if (isset($import_results)): ?>
        <div class="notice notice-info">
            <h3><?php esc_html_e('Import Results', 'vlog-plugin'); ?></h3>
            <ul>
                <?php foreach ($import_results as $result): ?>
                    <li>
                        <?php if ($result['success']): ?>
                            <span style="color: green;">✓</span>
                            <?php printf(
                                __('Successfully imported: %s', 'vlog-plugin'),
                                '<strong>' . esc_html($result['title']) . '</strong>'
                            ); ?>
                            <a href="<?php echo get_edit_post_link($result['post_id']); ?>" target="_blank">
                                <?php esc_html_e('Edit', 'vlog-plugin'); ?>
                            </a>
                        <?php else: ?>
                            <span style="color: red;">✗</span>
                            <?php printf(
                                __('Failed to import %s: %s', 'vlog-plugin'),
                                '<code>' . esc_html($result['url']) . '</code>',
                                esc_html($result['error'])
                            ); ?>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <div class="vlog-import-container">
        <div class="vlog-import-form">
            <form method="post" action="">
                <?php wp_nonce_field('vlog_import_videos', 'import_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="video_urls"><?php esc_html_e('Video URLs', 'vlog-plugin'); ?></label>
                        </th>
                        <td>
                            <textarea id="video_urls" name="video_urls" rows="10" cols="50" class="large-text" 
                                      placeholder="<?php esc_attr_e('Enter video URLs, one per line...', 'vlog-plugin'); ?>"><?php echo isset($_POST['video_urls']) ? esc_textarea($_POST['video_urls']) : ''; ?></textarea>
                            <p class="description">
                                <?php esc_html_e('Enter YouTube or Rumble video URLs, one per line. The plugin will automatically extract video information.', 'vlog-plugin'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="video_category"><?php esc_html_e('Category', 'vlog-plugin'); ?></label>
                        </th>
                        <td>
                            <?php
                            wp_dropdown_categories([
                                'taxonomy' => 'vlog_category',
                                'name' => 'video_category',
                                'id' => 'video_category',
                                'show_option_none' => __('No category', 'vlog-plugin'),
                                'option_none_value' => 0,
                                'selected' => isset($_POST['video_category']) ? intval($_POST['video_category']) : 0,
                                'hierarchical' => true,
                            ]);
                            ?>
                            <p class="description">
                                <?php esc_html_e('Optionally assign all imported videos to a category.', 'vlog-plugin'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(__('Import Videos', 'vlog-plugin'), 'primary', 'import_videos'); ?>
            </form>
        </div>
        
        <div class="vlog-import-sidebar">
            <div class="postbox">
                <h3 class="hndle"><?php esc_html_e('Supported Platforms', 'vlog-plugin'); ?></h3>
                <div class="inside">
                    <ul>
                        <li><strong>YouTube</strong> - youtube.com, youtu.be</li>
                        <li><strong>Rumble</strong> - rumble.com</li>
                    </ul>
                </div>
            </div>
            
            <div class="postbox">
                <h3 class="hndle"><?php esc_html_e('Import Tips', 'vlog-plugin'); ?></h3>
                <div class="inside">
                    <ul>
                        <li><?php esc_html_e('Import up to 50 videos at once', 'vlog-plugin'); ?></li>
                        <li><?php esc_html_e('Videos will be published immediately', 'vlog-plugin'); ?></li>
                        <li><?php esc_html_e('Duplicate URLs will be skipped', 'vlog-plugin'); ?></li>
                        <li><?php esc_html_e('Video thumbnails are automatically downloaded', 'vlog-plugin'); ?></li>
                    </ul>
                </div>
            </div>
            
            <div class="postbox">
                <h3 class="hndle"><?php esc_html_e('Quick Actions', 'vlog-plugin'); ?></h3>
                <div class="inside">
                    <p>
                        <a href="<?php echo admin_url('edit.php?post_type=vlog'); ?>" class="button">
                            <?php esc_html_e('View All Videos', 'vlog-plugin'); ?>
                        </a>
                    </p>
                    <p>
                        <a href="<?php echo admin_url('edit-tags.php?taxonomy=vlog_category&post_type=vlog'); ?>" class="button">
                            <?php esc_html_e('Manage Categories', 'vlog-plugin'); ?>
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.vlog-import-container {
    display: flex;
    gap: 20px;
}

.vlog-import-form {
    flex: 1;
}

.vlog-import-sidebar {
    width: 300px;
    flex-shrink: 0;
}

.vlog-import-sidebar .postbox {
    margin-bottom: 20px;
}

.vlog-import-sidebar .inside ul {
    margin: 0;
    padding-left: 20px;
}

.vlog-import-sidebar .inside ul li {
    margin-bottom: 5px;
}

@media (max-width: 782px) {
    .vlog-import-container {
        flex-direction: column;
    }
    
    .vlog-import-sidebar {
        width: 100%;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Validate URLs before submission
    $('form').on('submit', function(e) {
        const urls = $('#video_urls').val().trim();
        if (!urls) {
            alert('<?php esc_js_e('Please enter at least one video URL.', 'vlog-plugin'); ?>');
            e.preventDefault();
            return false;
        }
        
        const urlLines = urls.split('\n').filter(line => line.trim());
        if (urlLines.length > 50) {
            alert('<?php esc_js_e('Please limit to 50 URLs per import.', 'vlog-plugin'); ?>');
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        $(this).find('input[type="submit"]').prop('disabled', true).val('<?php esc_js_e('Importing...', 'vlog-plugin'); ?>');
    });
});
</script>
