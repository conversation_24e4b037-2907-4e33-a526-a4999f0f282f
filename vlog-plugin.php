<?php
/**
 * Plugin Name: Professional Vlog Plugin
 * Plugin URI: https://anthropora.com/vlog-plugin
 * Description: A professional WordPress plugin for managing video blogs with YouTube and Rumble integration, featuring video galleries, categories, and pagination.
 * Version: 1.0.0
 * Author: Anthropora
 * Author URI: https://anthropora.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: vlog-plugin
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package Anthropora\VlogPlugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('VLOG_PLUGIN_VERSION', '1.0.0');
define('VLOG_PLUGIN_FILE', __FILE__);
define('VLOG_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('VLOG_PLUGIN_URL', plugin_dir_url(__FILE__));
define('VLOG_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Require Composer autoloader
if (file_exists(VLOG_PLUGIN_PATH . 'vendor/autoload.php')) {
    require_once VLOG_PLUGIN_PATH . 'vendor/autoload.php';
}

// Initialize the plugin
add_action('plugins_loaded', function() {
    if (class_exists('Anthropora\\VlogPlugin\\Plugin')) {
        $plugin = new Anthropora\VlogPlugin\Plugin();
        $plugin->init();
    }
});

// Activation hook
register_activation_hook(__FILE__, function() {
    if (class_exists('Anthropora\\VlogPlugin\\Activator')) {
        Anthropora\VlogPlugin\Activator::activate();
    }
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    if (class_exists('Anthropora\\VlogPlugin\\Deactivator')) {
        Anthropora\VlogPlugin\Deactivator::deactivate();
    }
});

// Uninstall hook
register_uninstall_hook(__FILE__, function() {
    if (class_exists('Anthropora\\VlogPlugin\\Uninstaller')) {
        Anthropora\VlogPlugin\Uninstaller::uninstall();
    }
});
