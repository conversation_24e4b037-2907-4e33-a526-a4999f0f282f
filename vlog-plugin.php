<?php
/**
 * Plugin Name: Professional Vlog Plugin
 * Plugin URI: https://anthropora.com/vlog-plugin
 * Description: A professional WordPress plugin for managing video blogs with YouTube and Rumble integration, featuring video galleries, categories, and pagination.
 * Version: 1.0.0
 * Author: Anthropora
 * Author URI: https://anthropora.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: vlog-plugin
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package Anthropora\VlogPlugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('VLOG_PLUGIN_VERSION', '1.0.0');
define('VLOG_PLUGIN_FILE', __FILE__);
define('VLOG_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('VLOG_PLUGIN_URL', plugin_dir_url(__FILE__));
define('VLOG_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Simple autoloader fallback if Composer is not available
if (!class_exists('Anthropora\\VlogPlugin\\Plugin')) {
    spl_autoload_register(function ($class) {
        $prefix = 'Anthropora\\VlogPlugin\\';
        $base_dir = VLOG_PLUGIN_PATH . 'src/';

        $len = strlen($prefix);
        if (strncmp($prefix, $class, $len) !== 0) {
            return;
        }

        $relative_class = substr($class, $len);
        $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';

        if (file_exists($file)) {
            require $file;
        }
    });
}

// Require Composer autoloader if available
if (file_exists(VLOG_PLUGIN_PATH . 'vendor/autoload.php')) {
    require_once VLOG_PLUGIN_PATH . 'vendor/autoload.php';
}

// Initialize the plugin with error handling
add_action('plugins_loaded', function() {
    try {
        // Debug: Check if main class exists
        if (!class_exists('Anthropora\\VlogPlugin\\Plugin')) {
            error_log('Vlog Plugin: Main Plugin class not found');
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>';
                echo esc_html__('Vlog Plugin: Plugin classes could not be loaded. Please check file permissions and ensure all files are present.', 'vlog-plugin');
                echo '</p></div>';
            });
            return;
        }

        // Try to initialize the plugin
        $plugin = new Anthropora\VlogPlugin\Plugin();
        $plugin->init();

        // Set success transient
        set_transient('vlog_plugin_loaded', true, 30);

        // Success notice
        add_action('admin_notices', function() {
            if (get_transient('vlog_plugin_loaded')) {
                delete_transient('vlog_plugin_loaded');
                echo '<div class="notice notice-success is-dismissible"><p>';
                echo esc_html__('Vlog Plugin loaded successfully!', 'vlog-plugin');
                echo '</p></div>';
            }
        });

    } catch (Exception $e) {
        error_log('Vlog Plugin Error: ' . $e->getMessage());
        add_action('admin_notices', function() use ($e) {
            echo '<div class="notice notice-error"><p>';
            echo esc_html__('Vlog Plugin Error: ', 'vlog-plugin') . esc_html($e->getMessage());
            echo '</p></div>';
        });
    } catch (Error $e) {
        error_log('Vlog Plugin Fatal Error: ' . $e->getMessage());
        add_action('admin_notices', function() use ($e) {
            echo '<div class="notice notice-error"><p>';
            echo esc_html__('Vlog Plugin Fatal Error: ', 'vlog-plugin') . esc_html($e->getMessage());
            echo '</p></div>';
        });
    }
});

// Activation hook with error handling
register_activation_hook(__FILE__, function() {
    try {
        if (class_exists('Anthropora\\VlogPlugin\\Activator')) {
            Anthropora\VlogPlugin\Activator::activate();
        }
    } catch (Exception $e) {
        wp_die(
            esc_html__('Plugin activation failed: ', 'vlog-plugin') . esc_html($e->getMessage()),
            esc_html__('Plugin Activation Error', 'vlog-plugin'),
            ['back_link' => true]
        );
    }
});

// Deactivation hook with error handling
register_deactivation_hook(__FILE__, function() {
    try {
        if (class_exists('Anthropora\\VlogPlugin\\Deactivator')) {
            Anthropora\VlogPlugin\Deactivator::deactivate();
        }
    } catch (Exception $e) {
        error_log('Vlog Plugin deactivation error: ' . $e->getMessage());
    }
});

// Uninstall hook with error handling
register_uninstall_hook(__FILE__, function() {
    try {
        if (class_exists('Anthropora\\VlogPlugin\\Uninstaller')) {
            Anthropora\VlogPlugin\Uninstaller::uninstall();
        }
    } catch (Exception $e) {
        error_log('Vlog Plugin uninstall error: ' . $e->getMessage());
    }
});
