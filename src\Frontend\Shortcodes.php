<?php
/**
 * Shortcodes Handler
 *
 * @package Anthropora\VlogPlugin\Frontend
 */

namespace Anthropora\VlogPlugin\Frontend;

/**
 * Handles plugin shortcodes
 */
class Shortcodes {

    /**
     * Initialize shortcodes
     */
    public function init(): void {
        add_shortcode('vlog_gallery', [$this, 'render_vlog_gallery']);
        add_shortcode('vlog_video', [$this, 'render_single_video']);
        add_shortcode('vlog_categories', [$this, 'render_category_list']);
        add_shortcode('vlog_featured', [$this, 'render_featured_videos']);
    }

    /**
     * Render vlog gallery shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Gallery HTML
     */
    public function render_vlog_gallery($atts): string {
        $atts = shortcode_atts([
            'posts_per_page' => get_option('vlog_plugin_options', [])['videos_per_page'] ?? 12,
            'category' => '',
            'tag' => '',
            'orderby' => 'date',
            'order' => 'DESC',
            'columns' => 3,
            'show_pagination' => 'true',
            'show_filters' => 'true',
            'featured_only' => 'false',
        ], $atts, 'vlog_gallery');

        // Build query args
        $args = [
            'post_type' => 'vlog',
            'post_status' => 'publish',
            'posts_per_page' => intval($atts['posts_per_page']),
            'orderby' => sanitize_text_field($atts['orderby']),
            'order' => sanitize_text_field($atts['order']),
            'meta_query' => [
                [
                    'key' => '_vlog_embed_url',
                    'compare' => 'EXISTS',
                ],
            ],
        ];

        // Add taxonomy filters
        $tax_query = [];
        
        if (!empty($atts['category'])) {
            $tax_query[] = [
                'taxonomy' => 'vlog_category',
                'field' => 'slug',
                'terms' => explode(',', $atts['category']),
            ];
        }

        if (!empty($atts['tag'])) {
            $tax_query[] = [
                'taxonomy' => 'vlog_tag',
                'field' => 'slug',
                'terms' => explode(',', $atts['tag']),
            ];
        }

        if (!empty($tax_query)) {
            $args['tax_query'] = $tax_query;
        }

        // Featured only filter
        if ($atts['featured_only'] === 'true') {
            $args['meta_query'][] = [
                'key' => '_vlog_featured_video',
                'value' => '1',
                'compare' => '=',
            ];
        }

        // Handle pagination
        $paged = get_query_var('paged') ? get_query_var('paged') : 1;
        $args['paged'] = $paged;

        $query = new \WP_Query($args);

        ob_start();
        ?>
        <div class="vlog-gallery" data-columns="<?php echo esc_attr($atts['columns']); ?>">
            <?php if ($atts['show_filters'] === 'true'): ?>
                <div class="vlog-filters">
                    <?php $this->render_gallery_filters($atts); ?>
                </div>
            <?php endif; ?>

            <div class="vlog-gallery-grid vlog-columns-<?php echo esc_attr($atts['columns']); ?>">
                <?php if ($query->have_posts()): ?>
                    <?php while ($query->have_posts()): $query->the_post(); ?>
                        <?php $this->render_gallery_item(); ?>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="vlog-no-videos">
                        <p><?php esc_html_e('No videos found.', 'vlog-plugin'); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($atts['show_pagination'] === 'true' && $query->max_num_pages > 1): ?>
                <div class="vlog-pagination">
                    <?php
                    echo paginate_links([
                        'total' => $query->max_num_pages,
                        'current' => $paged,
                        'format' => '?paged=%#%',
                        'prev_text' => __('&laquo; Previous', 'vlog-plugin'),
                        'next_text' => __('Next &raquo;', 'vlog-plugin'),
                    ]);
                    ?>
                </div>
            <?php endif; ?>

            <?php if ($query->max_num_pages > $paged): ?>
                <div class="vlog-load-more">
                    <button type="button" class="vlog-load-more-btn" 
                            data-page="<?php echo esc_attr($paged + 1); ?>"
                            data-category="<?php echo esc_attr($atts['category']); ?>"
                            data-posts-per-page="<?php echo esc_attr($atts['posts_per_page']); ?>">
                        <?php esc_html_e('Load More Videos', 'vlog-plugin'); ?>
                    </button>
                </div>
            <?php endif; ?>
        </div>
        <?php

        wp_reset_postdata();
        return ob_get_clean();
    }

    /**
     * Render single video shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Video HTML
     */
    public function render_single_video($atts): string {
        $atts = shortcode_atts([
            'id' => 0,
            'url' => '',
            'width' => '100%',
            'height' => 'auto',
            'autoplay' => 'false',
            'controls' => 'true',
        ], $atts, 'vlog_video');

        $post_id = intval($atts['id']);
        $embed_url = '';

        if ($post_id) {
            $embed_url = get_post_meta($post_id, '_vlog_embed_url', true);
        } elseif (!empty($atts['url'])) {
            // Parse URL on the fly
            $video_parser = new \Anthropora\VlogPlugin\API\VideoParser();
            $video_data = $video_parser->parse_video_url($atts['url']);
            
            if ($video_data) {
                $embed_url = $video_data['embed_url'];
            }
        }

        if (!$embed_url) {
            return '<p>' . esc_html__('Video not found.', 'vlog-plugin') . '</p>';
        }

        // Add parameters
        $url_params = [];
        
        if ($atts['autoplay'] === 'true') {
            $url_params['autoplay'] = '1';
        }
        
        if ($atts['controls'] === 'false') {
            $url_params['controls'] = '0';
        }

        if (!empty($url_params)) {
            $embed_url = add_query_arg($url_params, $embed_url);
        }

        ob_start();
        ?>
        <div class="vlog-single-video" style="width: <?php echo esc_attr($atts['width']); ?>;">
            <div class="vlog-video-wrapper">
                <iframe 
                    src="<?php echo esc_url($embed_url); ?>"
                    frameborder="0"
                    allowfullscreen
                    loading="lazy"
                    style="width: 100%; height: <?php echo esc_attr($atts['height']); ?>;">
                </iframe>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render category list shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Categories HTML
     */
    public function render_category_list($atts): string {
        $atts = shortcode_atts([
            'show_count' => 'true',
            'hide_empty' => 'true',
            'orderby' => 'name',
            'order' => 'ASC',
            'style' => 'list', // list, grid, dropdown
        ], $atts, 'vlog_categories');

        $categories = get_terms([
            'taxonomy' => 'vlog_category',
            'hide_empty' => $atts['hide_empty'] === 'true',
            'orderby' => $atts['orderby'],
            'order' => $atts['order'],
        ]);

        if (empty($categories) || is_wp_error($categories)) {
            return '<p>' . esc_html__('No categories found.', 'vlog-plugin') . '</p>';
        }

        ob_start();
        ?>
        <div class="vlog-categories vlog-categories-<?php echo esc_attr($atts['style']); ?>">
            <?php if ($atts['style'] === 'dropdown'): ?>
                <select class="vlog-category-dropdown" onchange="location = this.value;">
                    <option value=""><?php esc_html_e('Select Category', 'vlog-plugin'); ?></option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo esc_url(get_term_link($category)); ?>">
                            <?php echo esc_html($category->name); ?>
                            <?php if ($atts['show_count'] === 'true'): ?>
                                (<?php echo esc_html($category->count); ?>)
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            <?php else: ?>
                <<?php echo $atts['style'] === 'list' ? 'ul' : 'div'; ?> class="vlog-category-<?php echo esc_attr($atts['style']); ?>">
                    <?php foreach ($categories as $category): ?>
                        <<?php echo $atts['style'] === 'list' ? 'li' : 'div'; ?> class="vlog-category-item">
                            <a href="<?php echo esc_url(get_term_link($category)); ?>" class="vlog-category-link">
                                <?php echo esc_html($category->name); ?>
                                <?php if ($atts['show_count'] === 'true'): ?>
                                    <span class="vlog-category-count">(<?php echo esc_html($category->count); ?>)</span>
                                <?php endif; ?>
                            </a>
                        </<?php echo $atts['style'] === 'list' ? 'li' : 'div'; ?>>
                    <?php endforeach; ?>
                </<?php echo $atts['style'] === 'list' ? 'ul' : 'div'; ?>>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render featured videos shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Featured videos HTML
     */
    public function render_featured_videos($atts): string {
        $atts = shortcode_atts([
            'limit' => 6,
            'columns' => 3,
            'orderby' => 'date',
            'order' => 'DESC',
        ], $atts, 'vlog_featured');

        $args = [
            'post_type' => 'vlog',
            'post_status' => 'publish',
            'posts_per_page' => intval($atts['limit']),
            'orderby' => sanitize_text_field($atts['orderby']),
            'order' => sanitize_text_field($atts['order']),
            'meta_query' => [
                [
                    'key' => '_vlog_featured_video',
                    'value' => '1',
                    'compare' => '=',
                ],
                [
                    'key' => '_vlog_embed_url',
                    'compare' => 'EXISTS',
                ],
            ],
        ];

        $query = new \WP_Query($args);

        if (!$query->have_posts()) {
            return '<p>' . esc_html__('No featured videos found.', 'vlog-plugin') . '</p>';
        }

        ob_start();
        ?>
        <div class="vlog-featured-videos">
            <div class="vlog-gallery-grid vlog-columns-<?php echo esc_attr($atts['columns']); ?>">
                <?php while ($query->have_posts()): $query->the_post(); ?>
                    <?php $this->render_gallery_item(true); ?>
                <?php endwhile; ?>
            </div>
        </div>
        <?php

        wp_reset_postdata();
        return ob_get_clean();
    }

    /**
     * Render gallery filters
     *
     * @param array $atts Shortcode attributes
     */
    private function render_gallery_filters($atts): void {
        $categories = get_terms([
            'taxonomy' => 'vlog_category',
            'hide_empty' => true,
        ]);

        if (!empty($categories) && !is_wp_error($categories)) {
            ?>
            <div class="vlog-filter-categories">
                <label><?php esc_html_e('Filter by Category:', 'vlog-plugin'); ?></label>
                <select class="vlog-category-filter">
                    <option value=""><?php esc_html_e('All Categories', 'vlog-plugin'); ?></option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo esc_attr($category->slug); ?>" 
                                <?php selected($atts['category'], $category->slug); ?>>
                            <?php echo esc_html($category->name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php
        }
    }

    /**
     * Render gallery item
     *
     * @param bool $is_featured Whether this is a featured video
     */
    private function render_gallery_item($is_featured = false): void {
        $post_id = get_the_ID();
        $thumbnail = get_post_meta($post_id, '_vlog_custom_thumbnail', true);
        
        if (!$thumbnail) {
            $thumbnail = get_post_meta($post_id, '_vlog_thumbnail_url', true);
        }
        
        $duration = get_post_meta($post_id, '_vlog_duration', true);
        $platform = get_post_meta($post_id, '_vlog_platform', true);
        $author = get_post_meta($post_id, '_vlog_author', true);
        $published_at = get_post_meta($post_id, '_vlog_published_at', true);
        $embed_url = get_post_meta($post_id, '_vlog_embed_url', true);
        
        $options = get_option('vlog_plugin_options', []);
        ?>
        <div class="vlog-gallery-item <?php echo $is_featured ? 'vlog-featured' : ''; ?>" data-video-id="<?php echo esc_attr($post_id); ?>">
            <div class="vlog-item-thumbnail">
                <?php if ($thumbnail): ?>
                    <img src="<?php echo esc_url($thumbnail); ?>" 
                         alt="<?php echo esc_attr(get_the_title()); ?>"
                         loading="lazy" />
                <?php endif; ?>
                
                <?php if ($duration && ($options['show_video_duration'] ?? true)): ?>
                    <span class="vlog-duration"><?php echo esc_html($this->format_duration($duration)); ?></span>
                <?php endif; ?>
                
                <?php if ($platform): ?>
                    <span class="vlog-platform vlog-platform-<?php echo esc_attr($platform); ?>">
                        <?php echo esc_html(ucfirst($platform)); ?>
                    </span>
                <?php endif; ?>
                
                <div class="vlog-play-overlay">
                    <span class="vlog-play-button" data-embed-url="<?php echo esc_attr($embed_url); ?>">▶</span>
                </div>
            </div>
            
            <div class="vlog-item-content">
                <h3 class="vlog-item-title">
                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                </h3>
                
                <?php if (has_excerpt()): ?>
                    <div class="vlog-item-excerpt">
                        <?php the_excerpt(); ?>
                    </div>
                <?php endif; ?>
                
                <div class="vlog-item-meta">
                    <?php if ($author): ?>
                        <span class="vlog-author"><?php echo esc_html($author); ?></span>
                    <?php endif; ?>
                    
                    <?php if ($published_at && ($options['show_video_date'] ?? true)): ?>
                        <span class="vlog-date"><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($published_at))); ?></span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Format duration in human readable format
     *
     * @param int $seconds Duration in seconds
     * @return string Formatted duration
     */
    private function format_duration($seconds): string {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%d:%02d:%02d', $hours, $minutes, $secs);
        } else {
            return sprintf('%d:%02d', $minutes, $secs);
        }
    }
}
