<?php
/**
 * Main Plugin Class
 *
 * @package Anthropora\VlogPlugin
 */

namespace Anthropora\VlogPlugin;

use Anthropora\VlogPlugin\Core\Container;
use Anthropora\VlogPlugin\Core\Loader;
use Anthropora\VlogPlugin\Admin\AdminManager;
use Anthropora\VlogPlugin\Frontend\FrontendManager;
use Anthropora\VlogPlugin\Database\DatabaseManager;
use Anthropora\VlogPlugin\API\VideoParser;

/**
 * Main Plugin Class
 */
class Plugin {

    /**
     * Plugin version
     */
    const VERSION = '1.0.0';

    /**
     * Dependency injection container
     *
     * @var Container
     */
    private $container;

    /**
     * Hook loader
     *
     * @var Loader
     */
    private $loader;

    /**
     * Plugin constructor
     */
    public function __construct() {
        $this->container = new Container();
        $this->loader = new Loader();
        $this->register_dependencies();
    }

    /**
     * Initialize the plugin
     */
    public function init(): void {
        try {
            $this->load_textdomain();
            $this->define_admin_hooks();
            $this->define_frontend_hooks();
            $this->define_common_hooks();
            $this->loader->run();
        } catch (Exception $e) {
            error_log('Vlog Plugin initialization error: ' . $e->getMessage());
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p>';
                echo esc_html__('Vlog Plugin initialization failed: ', 'vlog-plugin') . esc_html($e->getMessage());
                echo '</p></div>';
            });
        }
    }

    /**
     * Register dependencies in the container
     */
    private function register_dependencies(): void {
        try {
            // Register core services
            $this->container->register('database_manager', function() {
                return new DatabaseManager();
            });

            $this->container->register('video_parser', function() {
                return new VideoParser();
            });

            // Register managers
            $this->container->register('admin_manager', function() {
                return new AdminManager(
                    $this->container->get('database_manager'),
                    $this->container->get('video_parser')
                );
            });

            $this->container->register('frontend_manager', function() {
                return new FrontendManager(
                    $this->container->get('database_manager')
                );
            });
        } catch (Exception $e) {
            error_log('Vlog Plugin dependency registration error: ' . $e->getMessage());
        }
    }

    /**
     * Load plugin textdomain for internationalization
     */
    private function load_textdomain(): void {
        $this->loader->add_action('plugins_loaded', $this, 'load_plugin_textdomain');
    }

    /**
     * Load plugin textdomain callback
     */
    public function load_plugin_textdomain(): void {
        load_plugin_textdomain(
            'vlog-plugin',
            false,
            dirname(plugin_basename(VLOG_PLUGIN_FILE)) . '/languages/'
        );
    }

    /**
     * Define admin hooks
     */
    private function define_admin_hooks(): void {
        $admin_manager = $this->container->get('admin_manager');
        $this->loader->add_action('admin_init', $admin_manager, 'init');
        $this->loader->add_action('admin_menu', $admin_manager, 'add_admin_menu');
        $this->loader->add_action('admin_enqueue_scripts', $admin_manager, 'enqueue_scripts');
    }

    /**
     * Define frontend hooks
     */
    private function define_frontend_hooks(): void {
        $frontend_manager = $this->container->get('frontend_manager');
        $this->loader->add_action('wp_enqueue_scripts', $frontend_manager, 'enqueue_scripts');
        $this->loader->add_action('init', $frontend_manager, 'init');
    }

    /**
     * Define common hooks
     */
    private function define_common_hooks(): void {
        $database_manager = $this->container->get('database_manager');
        $this->loader->add_action('init', $database_manager, 'register_post_types');
        $this->loader->add_action('init', $database_manager, 'register_taxonomies');
    }

    /**
     * Get container instance
     *
     * @return Container
     */
    public function get_container(): Container {
        return $this->container;
    }

    /**
     * Get plugin version
     *
     * @return string
     */
    public function get_version(): string {
        return self::VERSION;
    }
}
