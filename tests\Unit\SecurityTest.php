<?php
/**
 * Security Tests
 *
 * @package Anthropora\VlogPlugin\Tests\Unit
 */

namespace Anthropora\VlogPlugin\Tests\Unit;

use Anthropora\VlogPlugin\Tests\TestCase;
use Anthropora\VlogPlugin\Core\Security;

/**
 * Test security functionality
 */
class SecurityTest extends TestCase {

    /**
     * Test video URL validation
     */
    public function testVideoUrlValidation(): void {
        // Valid URLs
        $valid_urls = [
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'https://youtu.be/dQw4w9WgXcQ',
            'https://rumble.com/v123abc-test.html',
            'https://www.rumble.com/embed/v123abc/',
        ];

        foreach ($valid_urls as $url) {
            $result = Security::validate_video_url($url);
            $this->assertNotFalse($result, "Valid URL should pass validation: $url");
            $this->assertEquals($url, $result);
        }

        // Invalid URLs
        $invalid_urls = [
            '',
            'not-a-url',
            'javascript:alert("xss")',
            'data:text/html,<script>alert("xss")</script>',
            'https://malicious-site.com/video',
            'ftp://example.com/video',
        ];

        foreach ($invalid_urls as $url) {
            $result = Security::validate_video_url($url);
            $this->assertFalse($result, "Invalid URL should fail validation: $url");
        }
    }

    /**
     * Test video data sanitization
     */
    public function testVideoDataSanitization(): void {
        $raw_data = [
            'title' => '<script>alert("xss")</script>Test Title',
            'description' => '<p>Valid HTML</p><script>alert("xss")</script>',
            'thumbnail' => 'javascript:alert("xss")',
            'duration' => '212.5',
            'video_id' => '<script>test123</script>',
            'embed_url' => 'https://www.youtube.com/embed/test123',
            'platform' => 'you<script>tube',
            'original_url' => 'https://www.youtube.com/watch?v=test123',
            'author' => '<b>Test Author</b>',
            'published_at' => '2023-01-01 12:00:00',
            'view_count' => '1000.5',
        ];

        $sanitized = Security::sanitize_video_data($raw_data);

        // Check sanitization
        $this->assertEquals('Test Title', $sanitized['title']);
        $this->assertEquals('<p>Valid HTML</p>', $sanitized['description']);
        $this->assertFalse($sanitized['thumbnail']); // Invalid URL should be removed
        $this->assertEquals(212, $sanitized['duration']);
        $this->assertEquals('test123', $sanitized['video_id']);
        $this->assertEquals('https://www.youtube.com/embed/test123', $sanitized['embed_url']);
        $this->assertEquals('youtube', $sanitized['platform']);
        $this->assertEquals('Test Author', $sanitized['author']);
        $this->assertEquals(1000, $sanitized['view_count']);
    }

    /**
     * Test permission checking
     */
    public function testPermissionChecking(): void {
        // Test without user
        $this->assertFalse(Security::check_vlog_permissions('create'));
        $this->assertFalse(Security::check_vlog_permissions('edit'));
        $this->assertFalse(Security::check_vlog_permissions('delete'));
        $this->assertFalse(Security::check_vlog_permissions('manage_settings'));

        // Create test user with editor role
        $user_id = $this->factory->user->create(['role' => 'editor']);
        wp_set_current_user($user_id);

        $this->assertTrue(Security::check_vlog_permissions('create'));
        $this->assertTrue(Security::check_vlog_permissions('edit'));
        $this->assertTrue(Security::check_vlog_permissions('delete'));
        $this->assertFalse(Security::check_vlog_permissions('manage_settings')); // Only admins

        // Test with admin
        $admin_id = $this->factory->user->create(['role' => 'administrator']);
        wp_set_current_user($admin_id);

        $this->assertTrue(Security::check_vlog_permissions('manage_settings'));
    }

    /**
     * Test nonce creation and verification
     */
    public function testNonceHandling(): void {
        $action = 'test_action';
        $nonce = Security::create_nonce($action);

        $this->assertNotEmpty($nonce);
        $this->assertTrue(Security::verify_nonce($nonce, $action));
        $this->assertFalse(Security::verify_nonce($nonce, 'different_action'));
        $this->assertFalse(Security::verify_nonce('invalid_nonce', $action));
    }

    /**
     * Test rate limiting
     */
    public function testRateLimit(): void {
        $key = 'test_key';
        $limit = 3;

        // Should allow first few requests
        for ($i = 0; $i < $limit; $i++) {
            $this->assertTrue(Security::rate_limit($key, $limit), "Request $i should be allowed");
        }

        // Should block additional requests
        $this->assertFalse(Security::rate_limit($key, $limit), "Request should be rate limited");
    }

    /**
     * Test import data validation
     */
    public function testImportDataValidation(): void {
        $urls = [
            'https://www.youtube.com/watch?v=valid1',
            'https://rumble.com/vvalid2-test.html',
            'javascript:alert("xss")',
            'https://malicious.com/video',
            'https://www.youtube.com/watch?v=valid3',
            '', // Empty URL
        ];

        $validated = Security::validate_import_data($urls);

        $this->assertCount(3, $validated); // Only valid URLs
        $this->assertContains('https://www.youtube.com/watch?v=valid1', $validated);
        $this->assertContains('https://rumble.com/vvalid2-test.html', $validated);
        $this->assertContains('https://www.youtube.com/watch?v=valid3', $validated);
    }

    /**
     * Test output escaping
     */
    public function testOutputEscaping(): void {
        $dangerous_data = '<script>alert("xss")</script>';

        $this->assertEquals('&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;', Security::escape_output($dangerous_data, 'html'));
        $this->assertEquals('&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;', Security::escape_output($dangerous_data, 'attr'));
        $this->assertEquals('alert(\"xss\")', Security::escape_output($dangerous_data, 'js'));
    }

    /**
     * Test thumbnail upload validation
     */
    public function testThumbnailUploadValidation(): void {
        // Mock valid image file
        $valid_file = [
            'name' => 'test.jpg',
            'type' => 'image/jpeg',
            'tmp_name' => '/tmp/test_image.jpg',
            'size' => 1024 * 1024, // 1MB
        ];

        // Create temporary test file
        $temp_file = tempnam(sys_get_temp_dir(), 'vlog_test_');
        file_put_contents($temp_file, 'fake image data');
        $valid_file['tmp_name'] = $temp_file;

        // Mock getimagesize
        add_filter('wp_check_filetype', function($data, $file, $filename) {
            return ['type' => 'image/jpeg', 'ext' => 'jpg'];
        }, 10, 3);

        // Test invalid file types
        $invalid_file = $valid_file;
        $invalid_file['type'] = 'application/javascript';
        $this->assertFalse(Security::validate_thumbnail_upload($invalid_file));

        // Test file too large
        $large_file = $valid_file;
        $large_file['size'] = 10 * 1024 * 1024; // 10MB
        $this->assertFalse(Security::validate_thumbnail_upload($large_file));

        // Clean up
        unlink($temp_file);
    }

    /**
     * Test XSS prevention in shortcode attributes
     */
    public function testShortcodeXssPrevention(): void {
        $malicious_atts = [
            'posts_per_page' => '12"><script>alert("xss")</script>',
            'category' => 'test-category\' onmouseover="alert(\'xss\')"',
            'columns' => '3</div><script>alert("xss")</script>',
        ];

        // Test that shortcode attributes are properly sanitized
        foreach ($malicious_atts as $key => $value) {
            $sanitized = sanitize_text_field($value);
            $this->assertStringNotContainsString('<script>', $sanitized);
            $this->assertStringNotContainsString('onmouseover', $sanitized);
        }
    }

    /**
     * Test SQL injection prevention
     */
    public function testSqlInjectionPrevention(): void {
        global $wpdb;

        $malicious_input = "'; DROP TABLE {$wpdb->posts}; --";
        
        // Test that wpdb prepare prevents SQL injection
        $query = $wpdb->prepare(
            "SELECT * FROM {$wpdb->posts} WHERE post_title = %s",
            $malicious_input
        );

        $this->assertStringNotContainsString('DROP TABLE', $query);
        $this->assertStringContainsString("post_title = '", $query);
    }

    /**
     * Test CSRF protection
     */
    public function testCsrfProtection(): void {
        // Simulate AJAX request without nonce
        $_POST['action'] = 'parse_video_url';
        $_POST['url'] = 'https://www.youtube.com/watch?v=test';

        // Should fail without proper nonce
        $this->expectException(\WPDieException::class);
        
        // Simulate the AJAX handler check
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'parse_video_url_nonce')) {
            wp_die('Security check failed');
        }
    }

    /**
     * Test file inclusion vulnerabilities
     */
    public function testFileInclusionPrevention(): void {
        // Test that direct file access is prevented
        $plugin_files = [
            VLOG_PLUGIN_PATH . 'src/Plugin.php',
            VLOG_PLUGIN_PATH . 'src/Core/Security.php',
            VLOG_PLUGIN_PATH . 'templates/single-vlog.php',
        ];

        foreach ($plugin_files as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                
                // Check for direct access prevention
                $this->assertTrue(
                    strpos($content, "if (!defined('ABSPATH'))") !== false ||
                    strpos($content, "defined('ABSPATH') || exit") !== false ||
                    strpos($content, 'namespace ') !== false, // Namespaced files are safer
                    "File should prevent direct access: $file"
                );
            }
        }
    }
}
