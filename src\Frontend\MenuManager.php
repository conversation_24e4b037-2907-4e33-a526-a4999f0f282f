<?php
/**
 * Menu Manager
 *
 * @package Anthropora\VlogPlugin\Frontend
 */

namespace Anthropora\VlogPlugin\Frontend;

/**
 * Handles menu integration and navigation
 */
class MenuManager {

    /**
     * Initialize menu manager
     */
    public function init(): void {
        add_action('wp_nav_menu_items', [$this, 'add_vlog_menu_items'], 10, 2);
        add_filter('wp_nav_menu_objects', [$this, 'add_vlog_submenu_items'], 10, 2);
        add_action('wp_update_nav_menu', [$this, 'clear_menu_cache']);
        add_action('created_vlog_category', [$this, 'clear_menu_cache']);
        add_action('edited_vlog_category', [$this, 'clear_menu_cache']);
        add_action('deleted_vlog_category', [$this, 'clear_menu_cache']);
    }

    /**
     * Add vlog menu items to navigation menus
     *
     * @param string $items Menu items HTML
     * @param object $args  Menu arguments
     * @return string Modified menu items
     */
    public function add_vlog_menu_items($items, $args): string {
        // Only add to primary menu or if specifically enabled
        if (!$this->should_add_vlog_menu($args)) {
            return $items;
        }

        $vlog_menu_item = $this->get_vlog_menu_item();
        
        if ($vlog_menu_item) {
            $items .= $vlog_menu_item;
        }

        return $items;
    }

    /**
     * Add vlog category submenu items
     *
     * @param array $items Menu items
     * @param object $args Menu arguments
     * @return array Modified menu items
     */
    public function add_vlog_submenu_items($items, $args): array {
        if (!$this->should_add_vlog_menu($args)) {
            return $items;
        }

        $vlog_page_id = get_option('vlog_plugin_main_page_id');
        
        if (!$vlog_page_id) {
            return $items;
        }

        // Find the vlog menu item
        $vlog_menu_item_id = null;
        foreach ($items as $item) {
            if ($item->object_id == $vlog_page_id && $item->object === 'page') {
                $vlog_menu_item_id = $item->ID;
                break;
            }
        }

        if (!$vlog_menu_item_id) {
            return $items;
        }

        // Get vlog categories
        $categories = $this->get_vlog_categories_for_menu();
        
        if (empty($categories)) {
            return $items;
        }

        // Add category submenu items
        $submenu_items = [];
        $menu_order = 1000; // Start with high number to appear after main item

        foreach ($categories as $category) {
            $submenu_item = $this->create_category_menu_item($category, $vlog_menu_item_id, $menu_order);
            $submenu_items[] = $submenu_item;
            $menu_order++;
        }

        // Merge with existing items
        return array_merge($items, $submenu_items);
    }

    /**
     * Clear menu cache when menus or categories are updated
     */
    public function clear_menu_cache(): void {
        delete_transient('vlog_menu_categories');
        
        // Clear WordPress menu cache
        wp_cache_delete('vlog_menu_items', 'vlog_plugin');
    }

    /**
     * Check if vlog menu should be added
     *
     * @param object $args Menu arguments
     * @return bool
     */
    private function should_add_vlog_menu($args): bool {
        $options = get_option('vlog_plugin_options', []);
        
        // Check if auto menu is enabled
        if (!($options['auto_add_to_menu'] ?? true)) {
            return false;
        }

        // Check if this is the primary menu or specified theme location
        $target_locations = apply_filters('vlog_plugin_menu_locations', ['primary', 'main', 'header']);
        
        if (isset($args->theme_location) && in_array($args->theme_location, $target_locations)) {
            return true;
        }

        return false;
    }

    /**
     * Get vlog menu item HTML
     *
     * @return string Menu item HTML
     */
    private function get_vlog_menu_item(): string {
        $vlog_page_id = get_option('vlog_plugin_main_page_id');
        
        if (!$vlog_page_id) {
            return '';
        }

        $page = get_post($vlog_page_id);
        
        if (!$page || $page->post_status !== 'publish') {
            return '';
        }

        $url = get_permalink($vlog_page_id);
        $title = $page->post_title;
        $current_class = '';

        // Check if current page
        if (is_page($vlog_page_id) || is_post_type_archive('vlog') || is_singular('vlog') || is_tax(['vlog_category', 'vlog_tag'])) {
            $current_class = ' current-menu-item';
        }

        return sprintf(
            '<li class="menu-item menu-item-type-page menu-item-object-page vlog-menu-item%s"><a href="%s">%s</a></li>',
            esc_attr($current_class),
            esc_url($url),
            esc_html($title)
        );
    }

    /**
     * Get vlog categories for menu
     *
     * @return array Categories
     */
    private function get_vlog_categories_for_menu(): array {
        $cache_key = 'vlog_menu_categories';
        $categories = get_transient($cache_key);

        if ($categories === false) {
            $categories = get_terms([
                'taxonomy' => 'vlog_category',
                'hide_empty' => true,
                'orderby' => 'name',
                'order' => 'ASC',
                'meta_query' => [
                    [
                        'key' => 'show_in_menu',
                        'value' => '1',
                        'compare' => '=',
                    ],
                ],
            ]);

            if (is_wp_error($categories)) {
                $categories = [];
            }

            // Cache for 1 hour
            set_transient($cache_key, $categories, HOUR_IN_SECONDS);
        }

        return $categories;
    }

    /**
     * Create category menu item object
     *
     * @param object $category        Category term
     * @param int    $parent_item_id  Parent menu item ID
     * @param int    $menu_order      Menu order
     * @return object Menu item object
     */
    private function create_category_menu_item($category, $parent_item_id, $menu_order): object {
        $menu_item = new \stdClass();
        
        $menu_item->ID = 'vlog-cat-' . $category->term_id;
        $menu_item->db_id = 'vlog-cat-' . $category->term_id;
        $menu_item->menu_item_parent = $parent_item_id;
        $menu_item->object_id = $category->term_id;
        $menu_item->object = 'vlog_category';
        $menu_item->type = 'taxonomy';
        $menu_item->type_label = __('Vlog Category', 'vlog-plugin');
        $menu_item->title = $category->name;
        $menu_item->url = get_term_link($category);
        $menu_item->target = '';
        $menu_item->attr_title = '';
        $menu_item->description = $category->description;
        $menu_item->classes = ['menu-item', 'menu-item-type-taxonomy', 'menu-item-object-vlog_category'];
        $menu_item->xfn = '';
        $menu_item->menu_order = $menu_order;
        $menu_item->current = is_tax('vlog_category', $category->slug);
        $menu_item->current_item_ancestor = false;
        $menu_item->current_item_parent = false;

        // Check if current category or ancestor
        if (is_tax('vlog_category')) {
            $current_term = get_queried_object();
            if ($current_term && $current_term->term_id === $category->term_id) {
                $menu_item->current = true;
                $menu_item->classes[] = 'current-menu-item';
            }
        }

        return $menu_item;
    }

    /**
     * Add vlog page to menu programmatically
     *
     * @param int $menu_id Menu ID
     * @return bool Success status
     */
    public function add_vlog_page_to_menu($menu_id): bool {
        $vlog_page_id = get_option('vlog_plugin_main_page_id');
        
        if (!$vlog_page_id) {
            return false;
        }

        // Check if already in menu
        $menu_items = wp_get_nav_menu_items($menu_id);
        
        foreach ($menu_items as $item) {
            if ($item->object_id == $vlog_page_id && $item->object === 'page') {
                return true; // Already exists
            }
        }

        // Add to menu
        $menu_item_data = [
            'menu-item-object-id' => $vlog_page_id,
            'menu-item-object' => 'page',
            'menu-item-type' => 'post_type',
            'menu-item-status' => 'publish',
        ];

        $menu_item_id = wp_update_nav_menu_item($menu_id, 0, $menu_item_data);

        return !is_wp_error($menu_item_id);
    }

    /**
     * Remove vlog items from menu
     *
     * @param int $menu_id Menu ID
     * @return bool Success status
     */
    public function remove_vlog_items_from_menu($menu_id): bool {
        $vlog_page_id = get_option('vlog_plugin_main_page_id');
        $menu_items = wp_get_nav_menu_items($menu_id);
        $removed = false;

        foreach ($menu_items as $item) {
            // Remove vlog page item
            if ($item->object_id == $vlog_page_id && $item->object === 'page') {
                wp_delete_post($item->ID, true);
                $removed = true;
            }
            
            // Remove vlog category items
            if ($item->object === 'vlog_category') {
                wp_delete_post($item->ID, true);
                $removed = true;
            }
        }

        return $removed;
    }

    /**
     * Get breadcrumb trail for vlog pages
     *
     * @return array Breadcrumb items
     */
    public function get_vlog_breadcrumbs(): array {
        $breadcrumbs = [];
        
        // Home
        $breadcrumbs[] = [
            'title' => __('Home', 'vlog-plugin'),
            'url' => home_url('/'),
            'current' => false,
        ];

        // Vlog main page
        $vlog_page_id = get_option('vlog_plugin_main_page_id');
        if ($vlog_page_id) {
            $vlog_page = get_post($vlog_page_id);
            $breadcrumbs[] = [
                'title' => $vlog_page->post_title,
                'url' => get_permalink($vlog_page_id),
                'current' => is_page($vlog_page_id),
            ];
        }

        // Category page
        if (is_tax('vlog_category')) {
            $term = get_queried_object();
            $breadcrumbs[] = [
                'title' => $term->name,
                'url' => get_term_link($term),
                'current' => true,
            ];
        }

        // Single vlog
        if (is_singular('vlog')) {
            $categories = get_the_terms(get_the_ID(), 'vlog_category');
            
            if ($categories && !is_wp_error($categories)) {
                $primary_category = $categories[0];
                $breadcrumbs[] = [
                    'title' => $primary_category->name,
                    'url' => get_term_link($primary_category),
                    'current' => false,
                ];
            }
            
            $breadcrumbs[] = [
                'title' => get_the_title(),
                'url' => get_permalink(),
                'current' => true,
            ];
        }

        return apply_filters('vlog_plugin_breadcrumbs', $breadcrumbs);
    }

    /**
     * Render breadcrumbs HTML
     *
     * @param array $args Breadcrumb arguments
     * @return string Breadcrumbs HTML
     */
    public function render_breadcrumbs($args = []): string {
        $defaults = [
            'separator' => ' &raquo; ',
            'home_text' => __('Home', 'vlog-plugin'),
            'before' => '<nav class="vlog-breadcrumbs">',
            'after' => '</nav>',
            'show_current' => true,
        ];

        $args = wp_parse_args($args, $defaults);
        $breadcrumbs = $this->get_vlog_breadcrumbs();

        if (count($breadcrumbs) <= 1) {
            return '';
        }

        $output = $args['before'];
        $total = count($breadcrumbs);

        foreach ($breadcrumbs as $index => $crumb) {
            $is_last = ($index + 1) === $total;
            
            if ($crumb['current'] && !$args['show_current']) {
                continue;
            }

            if (!$is_last) {
                $output .= sprintf(
                    '<a href="%s">%s</a>%s',
                    esc_url($crumb['url']),
                    esc_html($crumb['title']),
                    $args['separator']
                );
            } else {
                $output .= sprintf(
                    '<span class="current">%s</span>',
                    esc_html($crumb['title'])
                );
            }
        }

        $output .= $args['after'];

        return $output;
    }
}
