<?php
/**
 * Meta Boxes for Vlog Post Type
 *
 * @package Anthropora\VlogPlugin\Database
 */

namespace Anthropora\VlogPlugin\Database;

use Anthropora\VlogPlugin\API\VideoParser;

/**
 * Handles meta boxes for vlog posts
 */
class MetaBoxes {

    /**
     * Video parser instance
     *
     * @var VideoParser
     */
    private $video_parser;

    /**
     * Constructor
     *
     * @param VideoParser $video_parser Video parser instance
     */
    public function __construct(VideoParser $video_parser) {
        $this->video_parser = $video_parser;
    }

    /**
     * Initialize meta boxes
     */
    public function init(): void {
        add_action('add_meta_boxes', [$this, 'add_meta_boxes']);
        add_action('save_post', [$this, 'save_meta_boxes']);
        add_action('wp_ajax_parse_video_url', [$this, 'ajax_parse_video_url']);
    }

    /**
     * Add meta boxes
     */
    public function add_meta_boxes(): void {
        add_meta_box(
            'vlog_video_details',
            __('Video Details', 'vlog-plugin'),
            [$this, 'render_video_details_meta_box'],
            'vlog',
            'normal',
            'high'
        );

        add_meta_box(
            'vlog_video_settings',
            __('Video Settings', 'vlog-plugin'),
            [$this, 'render_video_settings_meta_box'],
            'vlog',
            'side',
            'default'
        );
    }

    /**
     * Render video details meta box
     *
     * @param \WP_Post $post Current post object
     */
    public function render_video_details_meta_box($post): void {
        wp_nonce_field('vlog_video_details_nonce', 'vlog_video_details_nonce');

        $video_url = get_post_meta($post->ID, '_vlog_video_url', true);
        $video_id = get_post_meta($post->ID, '_vlog_video_id', true);
        $platform = get_post_meta($post->ID, '_vlog_platform', true);
        $duration = get_post_meta($post->ID, '_vlog_duration', true);
        $embed_url = get_post_meta($post->ID, '_vlog_embed_url', true);
        $thumbnail_url = get_post_meta($post->ID, '_vlog_thumbnail_url', true);
        $author = get_post_meta($post->ID, '_vlog_author', true);
        $published_at = get_post_meta($post->ID, '_vlog_published_at', true);
        $view_count = get_post_meta($post->ID, '_vlog_view_count', true);

        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="vlog_video_url"><?php esc_html_e('Video URL', 'vlog-plugin'); ?></label>
                </th>
                <td>
                    <input type="url" id="vlog_video_url" name="vlog_video_url" 
                           value="<?php echo esc_attr($video_url); ?>" 
                           class="regular-text" placeholder="https://www.youtube.com/watch?v=..." />
                    <button type="button" id="parse_video_url" class="button">
                        <?php esc_html_e('Parse Video', 'vlog-plugin'); ?>
                    </button>
                    <p class="description">
                        <?php esc_html_e('Enter a YouTube or Rumble video URL and click "Parse Video" to automatically extract video information.', 'vlog-plugin'); ?>
                    </p>
                    <div id="parse_video_result"></div>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="vlog_platform"><?php esc_html_e('Platform', 'vlog-plugin'); ?></label>
                </th>
                <td>
                    <input type="text" id="vlog_platform" name="vlog_platform" 
                           value="<?php echo esc_attr($platform); ?>" 
                           class="regular-text" readonly />
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="vlog_video_id"><?php esc_html_e('Video ID', 'vlog-plugin'); ?></label>
                </th>
                <td>
                    <input type="text" id="vlog_video_id" name="vlog_video_id" 
                           value="<?php echo esc_attr($video_id); ?>" 
                           class="regular-text" readonly />
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="vlog_duration"><?php esc_html_e('Duration (seconds)', 'vlog-plugin'); ?></label>
                </th>
                <td>
                    <input type="number" id="vlog_duration" name="vlog_duration" 
                           value="<?php echo esc_attr($duration); ?>" 
                           class="small-text" min="0" />
                    <span id="duration_formatted"></span>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="vlog_author"><?php esc_html_e('Video Author', 'vlog-plugin'); ?></label>
                </th>
                <td>
                    <input type="text" id="vlog_author" name="vlog_author" 
                           value="<?php echo esc_attr($author); ?>" 
                           class="regular-text" />
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="vlog_published_at"><?php esc_html_e('Published Date', 'vlog-plugin'); ?></label>
                </th>
                <td>
                    <input type="datetime-local" id="vlog_published_at" name="vlog_published_at" 
                           value="<?php echo esc_attr($published_at ? date('Y-m-d\TH:i', strtotime($published_at)) : ''); ?>" 
                           class="regular-text" />
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="vlog_view_count"><?php esc_html_e('View Count', 'vlog-plugin'); ?></label>
                </th>
                <td>
                    <input type="number" id="vlog_view_count" name="vlog_view_count" 
                           value="<?php echo esc_attr($view_count); ?>" 
                           class="regular-text" min="0" />
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="vlog_thumbnail_url"><?php esc_html_e('Thumbnail URL', 'vlog-plugin'); ?></label>
                </th>
                <td>
                    <input type="url" id="vlog_thumbnail_url" name="vlog_thumbnail_url" 
                           value="<?php echo esc_attr($thumbnail_url); ?>" 
                           class="regular-text" />
                    <?php if ($thumbnail_url): ?>
                        <div class="thumbnail-preview" style="margin-top: 10px;">
                            <img src="<?php echo esc_url($thumbnail_url); ?>" 
                                 alt="<?php esc_attr_e('Video thumbnail', 'vlog-plugin'); ?>" 
                                 style="max-width: 200px; height: auto;" />
                        </div>
                    <?php endif; ?>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="vlog_embed_url"><?php esc_html_e('Embed URL', 'vlog-plugin'); ?></label>
                </th>
                <td>
                    <input type="url" id="vlog_embed_url" name="vlog_embed_url" 
                           value="<?php echo esc_attr($embed_url); ?>" 
                           class="regular-text" readonly />
                </td>
            </tr>
        </table>

        <script>
        jQuery(document).ready(function($) {
            // Format duration display
            function formatDuration(seconds) {
                if (!seconds) return '';
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;
                
                if (hours > 0) {
                    return `(${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')})`;
                } else {
                    return `(${minutes}:${secs.toString().padStart(2, '0')})`;
                }
            }
            
            $('#vlog_duration').on('input', function() {
                const duration = parseInt($(this).val()) || 0;
                $('#duration_formatted').text(formatDuration(duration));
            }).trigger('input');
            
            // Parse video URL
            $('#parse_video_url').on('click', function() {
                const url = $('#vlog_video_url').val();
                if (!url) {
                    alert('<?php esc_js_e('Please enter a video URL first.', 'vlog-plugin'); ?>');
                    return;
                }
                
                const $button = $(this);
                const $result = $('#parse_video_result');
                
                $button.prop('disabled', true).text('<?php esc_js_e('Parsing...', 'vlog-plugin'); ?>');
                $result.html('<p><?php esc_js_e('Parsing video URL...', 'vlog-plugin'); ?></p>');
                
                $.post(ajaxurl, {
                    action: 'parse_video_url',
                    url: url,
                    nonce: '<?php echo wp_create_nonce('parse_video_url_nonce'); ?>'
                }, function(response) {
                    if (response.success) {
                        const data = response.data;
                        $('#vlog_platform').val(data.platform);
                        $('#vlog_video_id').val(data.video_id);
                        $('#vlog_duration').val(data.duration).trigger('input');
                        $('#vlog_author').val(data.author);
                        $('#vlog_published_at').val(data.published_at);
                        $('#vlog_view_count').val(data.view_count);
                        $('#vlog_thumbnail_url').val(data.thumbnail);
                        $('#vlog_embed_url').val(data.embed_url);
                        
                        // Update post title if empty
                        if (!$('#title').val() && data.title) {
                            $('#title').val(data.title);
                        }
                        
                        $result.html('<p style="color: green;"><?php esc_js_e('Video parsed successfully!', 'vlog-plugin'); ?></p>');
                    } else {
                        $result.html('<p style="color: red;">' + response.data + '</p>');
                    }
                }).fail(function() {
                    $result.html('<p style="color: red;"><?php esc_js_e('Error parsing video URL.', 'vlog-plugin'); ?></p>');
                }).always(function() {
                    $button.prop('disabled', false).text('<?php esc_js_e('Parse Video', 'vlog-plugin'); ?>');
                });
            });
        });
        </script>
        <?php
    }

    /**
     * Render video settings meta box
     *
     * @param \WP_Post $post Current post object
     */
    public function render_video_settings_meta_box($post): void {
        wp_nonce_field('vlog_video_settings_nonce', 'vlog_video_settings_nonce');

        $featured_video = get_post_meta($post->ID, '_vlog_featured_video', true);
        $autoplay = get_post_meta($post->ID, '_vlog_autoplay', true);
        $show_controls = get_post_meta($post->ID, '_vlog_show_controls', true);
        $custom_thumbnail = get_post_meta($post->ID, '_vlog_custom_thumbnail', true);

        ?>
        <p>
            <label>
                <input type="checkbox" name="vlog_featured_video" value="1" <?php checked($featured_video, '1'); ?> />
                <?php esc_html_e('Featured Video', 'vlog-plugin'); ?>
            </label>
            <br><small><?php esc_html_e('Mark this video as featured to highlight it in galleries.', 'vlog-plugin'); ?></small>
        </p>
        
        <p>
            <label>
                <input type="checkbox" name="vlog_autoplay" value="1" <?php checked($autoplay, '1'); ?> />
                <?php esc_html_e('Autoplay Video', 'vlog-plugin'); ?>
            </label>
            <br><small><?php esc_html_e('Video will start playing automatically (may not work on all devices).', 'vlog-plugin'); ?></small>
        </p>
        
        <p>
            <label>
                <input type="checkbox" name="vlog_show_controls" value="1" <?php checked($show_controls, '1', false); ?> />
                <?php esc_html_e('Show Video Controls', 'vlog-plugin'); ?>
            </label>
            <br><small><?php esc_html_e('Display video player controls.', 'vlog-plugin'); ?></small>
        </p>
        
        <p>
            <label for="vlog_custom_thumbnail"><?php esc_html_e('Custom Thumbnail', 'vlog-plugin'); ?></label>
            <input type="url" id="vlog_custom_thumbnail" name="vlog_custom_thumbnail" 
                   value="<?php echo esc_attr($custom_thumbnail); ?>" 
                   class="widefat" placeholder="https://..." />
            <small><?php esc_html_e('Override the default video thumbnail with a custom image.', 'vlog-plugin'); ?></small>
        </p>
        <?php
    }

    /**
     * Save meta box data
     *
     * @param int $post_id Post ID
     */
    public function save_meta_boxes($post_id): void {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check post type
        if (get_post_type($post_id) !== 'vlog') {
            return;
        }

        // Check user permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save video details
        if (isset($_POST['vlog_video_details_nonce']) && wp_verify_nonce($_POST['vlog_video_details_nonce'], 'vlog_video_details_nonce')) {
            $fields = [
                'vlog_video_url', 'vlog_video_id', 'vlog_platform', 'vlog_duration',
                'vlog_embed_url', 'vlog_thumbnail_url', 'vlog_author', 
                'vlog_published_at', 'vlog_view_count'
            ];

            foreach ($fields as $field) {
                if (isset($_POST[$field])) {
                    update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
                }
            }
        }

        // Save video settings
        if (isset($_POST['vlog_video_settings_nonce']) && wp_verify_nonce($_POST['vlog_video_settings_nonce'], 'vlog_video_settings_nonce')) {
            $checkbox_fields = ['vlog_featured_video', 'vlog_autoplay', 'vlog_show_controls'];
            
            foreach ($checkbox_fields as $field) {
                update_post_meta($post_id, '_' . $field, isset($_POST[$field]) ? '1' : '0');
            }

            if (isset($_POST['vlog_custom_thumbnail'])) {
                update_post_meta($post_id, '_vlog_custom_thumbnail', esc_url_raw($_POST['vlog_custom_thumbnail']));
            }
        }
    }

    /**
     * AJAX handler for parsing video URLs
     */
    public function ajax_parse_video_url(): void {
        check_ajax_referer('parse_video_url_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_die(__('You do not have permission to perform this action.', 'vlog-plugin'));
        }

        $url = sanitize_url($_POST['url'] ?? '');
        
        if (empty($url)) {
            wp_send_json_error(__('Invalid URL provided.', 'vlog-plugin'));
        }

        $video_data = $this->video_parser->parse_video_url($url);
        
        if ($video_data) {
            wp_send_json_success($video_data);
        } else {
            wp_send_json_error(__('Unable to parse video URL. Please check the URL and try again.', 'vlog-plugin'));
        }
    }
}
