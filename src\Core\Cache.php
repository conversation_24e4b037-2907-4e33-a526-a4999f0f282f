<?php
/**
 * Cache Manager
 *
 * @package Anthropora\VlogPlugin\Core
 */

namespace Anthropora\VlogPlugin\Core;

/**
 * Handles caching strategies and performance optimization
 */
class Cache {

    /**
     * Cache group name
     */
    const CACHE_GROUP = 'vlog_plugin';

    /**
     * Initialize cache manager
     */
    public function init(): void {
        add_action('wp_ajax_vlog_clear_cache', [$this, 'ajax_clear_cache']);
        add_action('vlog_plugin_cleanup_cache', [$this, 'cleanup_expired_cache']);
        add_action('save_post', [$this, 'clear_post_cache'], 10, 2);
        add_action('deleted_post', [$this, 'clear_post_cache'], 10, 2);
        add_action('created_vlog_category', [$this, 'clear_taxonomy_cache']);
        add_action('edited_vlog_category', [$this, 'clear_taxonomy_cache']);
        add_action('deleted_vlog_category', [$this, 'clear_taxonomy_cache']);
    }

    /**
     * Get cached data
     *
     * @param string $key Cache key
     * @param string $group Cache group
     * @return mixed Cached data or false
     */
    public static function get($key, $group = self::CACHE_GROUP) {
        return wp_cache_get($key, $group);
    }

    /**
     * Set cached data
     *
     * @param string $key Cache key
     * @param mixed  $data Data to cache
     * @param int    $expiration Expiration time in seconds
     * @param string $group Cache group
     * @return bool Success status
     */
    public static function set($key, $data, $expiration = 3600, $group = self::CACHE_GROUP): bool {
        return wp_cache_set($key, $data, $group, $expiration);
    }

    /**
     * Delete cached data
     *
     * @param string $key Cache key
     * @param string $group Cache group
     * @return bool Success status
     */
    public static function delete($key, $group = self::CACHE_GROUP): bool {
        return wp_cache_delete($key, $group);
    }

    /**
     * Get video data from cache
     *
     * @param string $video_id Video ID
     * @param string $platform Platform name
     * @return array|false Video data or false
     */
    public static function get_video_data($video_id, $platform) {
        $cache_key = self::get_video_cache_key($video_id, $platform);
        return self::get($cache_key);
    }

    /**
     * Set video data in cache
     *
     * @param string $video_id Video ID
     * @param string $platform Platform name
     * @param array  $data Video data
     * @param int    $expiration Expiration time
     * @return bool Success status
     */
    public static function set_video_data($video_id, $platform, $data, $expiration = null): bool {
        if ($expiration === null) {
            $options = get_option('vlog_plugin_options', []);
            $expiration = $options['cache_duration'] ?? 3600;
        }

        $cache_key = self::get_video_cache_key($video_id, $platform);
        
        // Also store in database for persistent cache
        self::store_video_data_in_db($video_id, $platform, $data, $expiration);
        
        return self::set($cache_key, $data, $expiration);
    }

    /**
     * Get gallery cache
     *
     * @param array $args Query arguments
     * @return array|false Gallery data or false
     */
    public static function get_gallery_cache($args) {
        $cache_key = 'gallery_' . md5(serialize($args));
        return self::get($cache_key);
    }

    /**
     * Set gallery cache
     *
     * @param array $args Query arguments
     * @param array $data Gallery data
     * @param int   $expiration Expiration time
     * @return bool Success status
     */
    public static function set_gallery_cache($args, $data, $expiration = 1800): bool {
        $cache_key = 'gallery_' . md5(serialize($args));
        return self::set($cache_key, $data, $expiration);
    }

    /**
     * Clear all plugin cache
     *
     * @return bool Success status
     */
    public static function clear_all(): bool {
        global $wpdb;

        // Clear object cache
        wp_cache_flush_group(self::CACHE_GROUP);

        // Clear transients
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
                '_transient_vlog_plugin_%',
                '_transient_timeout_vlog_plugin_%'
            )
        );

        // Clear database cache
        $table_name = $wpdb->prefix . 'vlog_video_cache';
        $wpdb->query("TRUNCATE TABLE {$table_name}");

        return true;
    }

    /**
     * AJAX handler for clearing cache
     */
    public function ajax_clear_cache(): void {
        check_ajax_referer('vlog_clear_cache_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'vlog-plugin'));
        }

        $cleared = self::clear_all();

        if ($cleared) {
            wp_send_json_success(__('Cache cleared successfully.', 'vlog-plugin'));
        } else {
            wp_send_json_error(__('Failed to clear cache.', 'vlog-plugin'));
        }
    }

    /**
     * Cleanup expired cache entries
     */
    public function cleanup_expired_cache(): void {
        global $wpdb;

        $table_name = $wpdb->prefix . 'vlog_video_cache';
        
        // Delete expired entries
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$table_name} WHERE expires_at < %s",
                current_time('mysql')
            )
        );

        // Clean up old transients
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s AND option_value < %d",
                '_transient_timeout_vlog_plugin_%',
                time()
            )
        );
    }

    /**
     * Clear post-related cache
     *
     * @param int     $post_id Post ID
     * @param WP_Post $post Post object
     */
    public function clear_post_cache($post_id, $post): void {
        if ($post->post_type !== 'vlog') {
            return;
        }

        // Clear gallery caches
        wp_cache_delete('gallery_*', self::CACHE_GROUP);
        
        // Clear related video cache
        $video_id = get_post_meta($post_id, '_vlog_video_id', true);
        $platform = get_post_meta($post_id, '_vlog_platform', true);
        
        if ($video_id && $platform) {
            $cache_key = self::get_video_cache_key($video_id, $platform);
            self::delete($cache_key);
        }

        // Clear menu cache
        delete_transient('vlog_menu_categories');
    }

    /**
     * Clear taxonomy-related cache
     */
    public function clear_taxonomy_cache(): void {
        // Clear gallery caches
        wp_cache_delete('gallery_*', self::CACHE_GROUP);
        
        // Clear menu cache
        delete_transient('vlog_menu_categories');
        
        // Clear category-related caches
        wp_cache_delete('vlog_categories', self::CACHE_GROUP);
    }

    /**
     * Get video cache key
     *
     * @param string $video_id Video ID
     * @param string $platform Platform name
     * @return string Cache key
     */
    private static function get_video_cache_key($video_id, $platform): string {
        return 'video_' . $platform . '_' . $video_id;
    }

    /**
     * Store video data in database for persistent cache
     *
     * @param string $video_id Video ID
     * @param string $platform Platform name
     * @param array  $data Video data
     * @param int    $expiration Expiration time
     */
    private static function store_video_data_in_db($video_id, $platform, $data, $expiration): void {
        global $wpdb;

        $table_name = $wpdb->prefix . 'vlog_video_cache';
        $expires_at = date('Y-m-d H:i:s', time() + $expiration);

        $wpdb->replace(
            $table_name,
            [
                'video_id' => $video_id,
                'platform' => $platform,
                'title' => $data['title'] ?? '',
                'description' => $data['description'] ?? '',
                'thumbnail_url' => $data['thumbnail'] ?? '',
                'duration' => $data['duration'] ?? 0,
                'author' => $data['author'] ?? '',
                'published_at' => $data['published_at'] ?? null,
                'view_count' => $data['view_count'] ?? 0,
                'embed_url' => $data['embed_url'] ?? '',
                'original_url' => $data['original_url'] ?? '',
                'expires_at' => $expires_at,
            ],
            [
                '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%d', '%s', '%s', '%s'
            ]
        );
    }

    /**
     * Get video data from database cache
     *
     * @param string $video_id Video ID
     * @param string $platform Platform name
     * @return array|null Video data or null
     */
    public static function get_video_data_from_db($video_id, $platform): ?array {
        global $wpdb;

        $table_name = $wpdb->prefix . 'vlog_video_cache';
        
        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$table_name} WHERE video_id = %s AND platform = %s AND expires_at > %s",
                $video_id,
                $platform,
                current_time('mysql')
            ),
            ARRAY_A
        );

        if (!$result) {
            return null;
        }

        return [
            'title' => $result['title'],
            'description' => $result['description'],
            'thumbnail' => $result['thumbnail_url'],
            'duration' => (int) $result['duration'],
            'author' => $result['author'],
            'published_at' => $result['published_at'],
            'view_count' => (int) $result['view_count'],
            'embed_url' => $result['embed_url'],
            'original_url' => $result['original_url'],
            'video_id' => $result['video_id'],
            'platform' => $result['platform'],
        ];
    }

    /**
     * Preload critical data
     */
    public static function preload_critical_data(): void {
        // Preload featured videos
        $featured_args = [
            'post_type' => 'vlog',
            'post_status' => 'publish',
            'posts_per_page' => 6,
            'meta_query' => [
                [
                    'key' => '_vlog_featured_video',
                    'value' => '1',
                    'compare' => '=',
                ],
            ],
        ];

        $cache_key = 'featured_videos';
        $cached = self::get($cache_key);
        
        if ($cached === false) {
            $query = new \WP_Query($featured_args);
            self::set($cache_key, $query->posts, 1800); // 30 minutes
        }

        // Preload categories
        $categories = get_terms([
            'taxonomy' => 'vlog_category',
            'hide_empty' => true,
        ]);

        self::set('vlog_categories', $categories, 3600); // 1 hour
    }

    /**
     * Get cache statistics
     *
     * @return array Cache statistics
     */
    public static function get_cache_stats(): array {
        global $wpdb;

        $table_name = $wpdb->prefix . 'vlog_video_cache';
        
        $total_entries = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
        $expired_entries = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$table_name} WHERE expires_at < %s",
                current_time('mysql')
            )
        );

        return [
            'total_entries' => (int) $total_entries,
            'active_entries' => (int) $total_entries - (int) $expired_entries,
            'expired_entries' => (int) $expired_entries,
            'cache_hit_ratio' => self::calculate_hit_ratio(),
        ];
    }

    /**
     * Calculate cache hit ratio (simplified)
     *
     * @return float Hit ratio percentage
     */
    private static function calculate_hit_ratio(): float {
        $hits = get_transient('vlog_cache_hits') ?: 0;
        $misses = get_transient('vlog_cache_misses') ?: 0;
        $total = $hits + $misses;

        if ($total === 0) {
            return 0.0;
        }

        return round(($hits / $total) * 100, 2);
    }

    /**
     * Record cache hit
     */
    public static function record_cache_hit(): void {
        $hits = get_transient('vlog_cache_hits') ?: 0;
        set_transient('vlog_cache_hits', $hits + 1, DAY_IN_SECONDS);
    }

    /**
     * Record cache miss
     */
    public static function record_cache_miss(): void {
        $misses = get_transient('vlog_cache_misses') ?: 0;
        set_transient('vlog_cache_misses', $misses + 1, DAY_IN_SECONDS);
    }
}
