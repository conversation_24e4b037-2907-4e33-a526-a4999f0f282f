<?php
/**
 * Admin Manager
 *
 * @package Anthropora\VlogPlugin\Admin
 */

namespace Anthropora\VlogPlugin\Admin;

use Anthropora\VlogPlugin\Database\DatabaseManager;
use Anthropora\VlogPlugin\Database\MetaBoxes;
use Anthropora\VlogPlugin\API\VideoParser;

/**
 * Handles admin functionality
 */
class AdminManager {

    /**
     * Database manager instance
     *
     * @var DatabaseManager
     */
    private $database_manager;

    /**
     * Video parser instance
     *
     * @var VideoParser
     */
    private $video_parser;

    /**
     * Meta boxes instance
     *
     * @var MetaBoxes
     */
    private $meta_boxes;

    /**
     * Settings page instance
     *
     * @var SettingsPage
     */
    private $settings_page;

    /**
     * Constructor
     *
     * @param DatabaseManager $database_manager Database manager
     * @param VideoParser     $video_parser     Video parser
     */
    public function __construct(DatabaseManager $database_manager, VideoParser $video_parser) {
        $this->database_manager = $database_manager;
        $this->video_parser = $video_parser;
        $this->meta_boxes = new MetaBoxes($video_parser);
        $this->settings_page = new SettingsPage();
    }

    /**
     * Initialize admin functionality
     */
    public function init(): void {
        $this->meta_boxes->init();
        $this->settings_page->init();
        
        add_action('admin_init', [$this, 'admin_init']);
        add_filter('manage_vlog_posts_columns', [$this, 'add_vlog_columns']);
        add_action('manage_vlog_posts_custom_column', [$this, 'populate_vlog_columns'], 10, 2);
        add_filter('manage_edit-vlog_sortable_columns', [$this, 'make_vlog_columns_sortable']);
        add_action('pre_get_posts', [$this, 'handle_vlog_column_sorting']);
        
        // Bulk actions
        add_filter('bulk_actions-edit-vlog', [$this, 'add_bulk_actions']);
        add_filter('handle_bulk_actions-edit-vlog', [$this, 'handle_bulk_actions'], 10, 3);
        
        // Admin notices
        add_action('admin_notices', [$this, 'show_admin_notices']);
    }

    /**
     * Admin init callback
     */
    public function admin_init(): void {
        // Add custom capabilities
        $this->add_custom_capabilities();
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu(): void {
        add_submenu_page(
            'edit.php?post_type=vlog',
            __('Vlog Settings', 'vlog-plugin'),
            __('Settings', 'vlog-plugin'),
            'manage_options',
            'vlog-settings',
            [$this->settings_page, 'render_page']
        );

        add_submenu_page(
            'edit.php?post_type=vlog',
            __('Import Videos', 'vlog-plugin'),
            __('Import', 'vlog-plugin'),
            'edit_posts',
            'vlog-import',
            [$this, 'render_import_page']
        );
    }

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook Current admin page hook
     */
    public function enqueue_scripts($hook): void {
        global $post_type;

        if ($post_type === 'vlog' || strpos($hook, 'vlog') !== false) {
            wp_enqueue_style(
                'vlog-admin-style',
                VLOG_PLUGIN_URL . 'assets/css/admin.css',
                [],
                VLOG_PLUGIN_VERSION
            );

            wp_enqueue_script(
                'vlog-admin-script',
                VLOG_PLUGIN_URL . 'assets/js/admin.js',
                ['jquery', 'wp-util'],
                VLOG_PLUGIN_VERSION,
                true
            );

            wp_localize_script('vlog-admin-script', 'vlogAdmin', [
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('vlog_admin_nonce'),
                'strings' => [
                    'confirmDelete' => __('Are you sure you want to delete this video?', 'vlog-plugin'),
                    'parseError' => __('Error parsing video URL.', 'vlog-plugin'),
                    'parseSuccess' => __('Video parsed successfully!', 'vlog-plugin'),
                ],
            ]);
        }
    }

    /**
     * Add custom columns to vlog post list
     *
     * @param array $columns Existing columns
     * @return array Modified columns
     */
    public function add_vlog_columns($columns): array {
        $new_columns = [];
        
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;
            
            if ($key === 'title') {
                $new_columns['vlog_thumbnail'] = __('Thumbnail', 'vlog-plugin');
                $new_columns['vlog_platform'] = __('Platform', 'vlog-plugin');
                $new_columns['vlog_duration'] = __('Duration', 'vlog-plugin');
                $new_columns['vlog_author'] = __('Video Author', 'vlog-plugin');
                $new_columns['vlog_views'] = __('Views', 'vlog-plugin');
            }
        }

        return $new_columns;
    }

    /**
     * Populate custom columns
     *
     * @param string $column  Column name
     * @param int    $post_id Post ID
     */
    public function populate_vlog_columns($column, $post_id): void {
        switch ($column) {
            case 'vlog_thumbnail':
                $thumbnail = get_post_meta($post_id, '_vlog_custom_thumbnail', true);
                if (!$thumbnail) {
                    $thumbnail = get_post_meta($post_id, '_vlog_thumbnail_url', true);
                }
                
                if ($thumbnail) {
                    echo '<img src="' . esc_url($thumbnail) . '" alt="' . esc_attr__('Video thumbnail', 'vlog-plugin') . '" style="width: 60px; height: auto;" />';
                } else {
                    echo '—';
                }
                break;

            case 'vlog_platform':
                $platform = get_post_meta($post_id, '_vlog_platform', true);
                if ($platform) {
                    echo '<span class="vlog-platform vlog-platform-' . esc_attr($platform) . '">' . esc_html(ucfirst($platform)) . '</span>';
                } else {
                    echo '—';
                }
                break;

            case 'vlog_duration':
                $duration = get_post_meta($post_id, '_vlog_duration', true);
                if ($duration) {
                    echo $this->format_duration($duration);
                } else {
                    echo '—';
                }
                break;

            case 'vlog_author':
                $author = get_post_meta($post_id, '_vlog_author', true);
                echo $author ? esc_html($author) : '—';
                break;

            case 'vlog_views':
                $views = get_post_meta($post_id, '_vlog_view_count', true);
                echo $views ? number_format_i18n($views) : '—';
                break;
        }
    }

    /**
     * Make columns sortable
     *
     * @param array $columns Sortable columns
     * @return array Modified columns
     */
    public function make_vlog_columns_sortable($columns): array {
        $columns['vlog_platform'] = 'vlog_platform';
        $columns['vlog_duration'] = 'vlog_duration';
        $columns['vlog_author'] = 'vlog_author';
        $columns['vlog_views'] = 'vlog_views';
        
        return $columns;
    }

    /**
     * Handle column sorting
     *
     * @param \WP_Query $query Current query
     */
    public function handle_vlog_column_sorting($query): void {
        if (!is_admin() || !$query->is_main_query()) {
            return;
        }

        $orderby = $query->get('orderby');
        
        switch ($orderby) {
            case 'vlog_platform':
                $query->set('meta_key', '_vlog_platform');
                $query->set('orderby', 'meta_value');
                break;
                
            case 'vlog_duration':
                $query->set('meta_key', '_vlog_duration');
                $query->set('orderby', 'meta_value_num');
                break;
                
            case 'vlog_author':
                $query->set('meta_key', '_vlog_author');
                $query->set('orderby', 'meta_value');
                break;
                
            case 'vlog_views':
                $query->set('meta_key', '_vlog_view_count');
                $query->set('orderby', 'meta_value_num');
                break;
        }
    }

    /**
     * Add bulk actions
     *
     * @param array $actions Existing actions
     * @return array Modified actions
     */
    public function add_bulk_actions($actions): array {
        $actions['refresh_video_data'] = __('Refresh Video Data', 'vlog-plugin');
        $actions['mark_featured'] = __('Mark as Featured', 'vlog-plugin');
        $actions['unmark_featured'] = __('Remove Featured', 'vlog-plugin');
        
        return $actions;
    }

    /**
     * Handle bulk actions
     *
     * @param string $redirect_to Redirect URL
     * @param string $doaction    Action name
     * @param array  $post_ids    Selected post IDs
     * @return string Modified redirect URL
     */
    public function handle_bulk_actions($redirect_to, $doaction, $post_ids): string {
        if (empty($post_ids)) {
            return $redirect_to;
        }

        switch ($doaction) {
            case 'refresh_video_data':
                $count = $this->refresh_video_data($post_ids);
                $redirect_to = add_query_arg('vlog_refreshed', $count, $redirect_to);
                break;
                
            case 'mark_featured':
                $count = $this->mark_as_featured($post_ids, true);
                $redirect_to = add_query_arg('vlog_featured', $count, $redirect_to);
                break;
                
            case 'unmark_featured':
                $count = $this->mark_as_featured($post_ids, false);
                $redirect_to = add_query_arg('vlog_unfeatured', $count, $redirect_to);
                break;
        }

        return $redirect_to;
    }

    /**
     * Show admin notices
     */
    public function show_admin_notices(): void {
        if (isset($_GET['vlog_refreshed'])) {
            $count = intval($_GET['vlog_refreshed']);
            echo '<div class="notice notice-success is-dismissible"><p>';
            printf(
                _n(
                    '%d video data refreshed.',
                    '%d videos data refreshed.',
                    $count,
                    'vlog-plugin'
                ),
                $count
            );
            echo '</p></div>';
        }

        if (isset($_GET['vlog_featured'])) {
            $count = intval($_GET['vlog_featured']);
            echo '<div class="notice notice-success is-dismissible"><p>';
            printf(
                _n(
                    '%d video marked as featured.',
                    '%d videos marked as featured.',
                    $count,
                    'vlog-plugin'
                ),
                $count
            );
            echo '</p></div>';
        }

        if (isset($_GET['vlog_unfeatured'])) {
            $count = intval($_GET['vlog_unfeatured']);
            echo '<div class="notice notice-success is-dismissible"><p>';
            printf(
                _n(
                    '%d video unmarked as featured.',
                    '%d videos unmarked as featured.',
                    $count,
                    'vlog-plugin'
                ),
                $count
            );
            echo '</p></div>';
        }
    }

    /**
     * Render import page
     */
    public function render_import_page(): void {
        include VLOG_PLUGIN_PATH . 'templates/admin/import-page.php';
    }

    /**
     * Add custom capabilities
     */
    private function add_custom_capabilities(): void {
        $role = get_role('administrator');
        if ($role) {
            $role->add_cap('manage_vlog_settings');
        }
    }

    /**
     * Format duration in human readable format
     *
     * @param int $seconds Duration in seconds
     * @return string Formatted duration
     */
    private function format_duration($seconds): string {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%d:%02d:%02d', $hours, $minutes, $secs);
        } else {
            return sprintf('%d:%02d', $minutes, $secs);
        }
    }

    /**
     * Refresh video data for selected posts
     *
     * @param array $post_ids Post IDs
     * @return int Number of posts processed
     */
    private function refresh_video_data($post_ids): int {
        $count = 0;
        
        foreach ($post_ids as $post_id) {
            $video_url = get_post_meta($post_id, '_vlog_video_url', true);
            
            if ($video_url) {
                $video_data = $this->video_parser->parse_video_url($video_url);
                
                if ($video_data) {
                    foreach ($video_data as $key => $value) {
                        update_post_meta($post_id, '_vlog_' . $key, $value);
                    }
                    $count++;
                }
            }
        }
        
        return $count;
    }

    /**
     * Mark posts as featured or unfeatured
     *
     * @param array $post_ids  Post IDs
     * @param bool  $featured  Whether to mark as featured
     * @return int Number of posts processed
     */
    private function mark_as_featured($post_ids, $featured): int {
        $count = 0;
        
        foreach ($post_ids as $post_id) {
            update_post_meta($post_id, '_vlog_featured_video', $featured ? '1' : '0');
            $count++;
        }
        
        return $count;
    }
}
