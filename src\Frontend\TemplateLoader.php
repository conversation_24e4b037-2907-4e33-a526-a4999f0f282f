<?php
/**
 * Template Loader
 *
 * @package Anthropora\VlogPlugin\Frontend
 */

namespace Anthropora\VlogPlugin\Frontend;

/**
 * Handles template loading and hierarchy
 */
class TemplateLoader {

    /**
     * Initialize template loader
     */
    public function init(): void {
        add_filter('template_include', [$this, 'template_include']);
        add_filter('archive_template_hierarchy', [$this, 'add_archive_templates']);
        add_filter('single_template_hierarchy', [$this, 'add_single_templates']);
        add_filter('taxonomy_template_hierarchy', [$this, 'add_taxonomy_templates']);
    }

    /**
     * Include custom templates
     *
     * @param string $template Template path
     * @return string Modified template path
     */
    public function template_include($template): string {
        if (is_singular('vlog')) {
            $custom_template = $this->locate_template('single-vlog.php');
            if ($custom_template) {
                return $custom_template;
            }
        }

        if (is_post_type_archive('vlog')) {
            $custom_template = $this->locate_template('archive-vlog.php');
            if ($custom_template) {
                return $custom_template;
            }
        }

        if (is_tax('vlog_category')) {
            $custom_template = $this->locate_template('taxonomy-vlog_category.php');
            if ($custom_template) {
                return $custom_template;
            }
        }

        if (is_tax('vlog_tag')) {
            $custom_template = $this->locate_template('taxonomy-vlog_tag.php');
            if ($custom_template) {
                return $custom_template;
            }
        }

        return $template;
    }

    /**
     * Add archive templates to hierarchy
     *
     * @param array $templates Template hierarchy
     * @return array Modified hierarchy
     */
    public function add_archive_templates($templates): array {
        if (is_post_type_archive('vlog')) {
            array_unshift($templates, 'archive-vlog.php');
        }
        return $templates;
    }

    /**
     * Add single templates to hierarchy
     *
     * @param array $templates Template hierarchy
     * @return array Modified hierarchy
     */
    public function add_single_templates($templates): array {
        global $post;
        
        if ($post && $post->post_type === 'vlog') {
            array_unshift($templates, 'single-vlog.php');
        }
        
        return $templates;
    }

    /**
     * Add taxonomy templates to hierarchy
     *
     * @param array $templates Template hierarchy
     * @return array Modified hierarchy
     */
    public function add_taxonomy_templates($templates): array {
        $queried_object = get_queried_object();
        
        if ($queried_object && isset($queried_object->taxonomy)) {
            if ($queried_object->taxonomy === 'vlog_category') {
                array_unshift($templates, 'taxonomy-vlog_category.php');
            } elseif ($queried_object->taxonomy === 'vlog_tag') {
                array_unshift($templates, 'taxonomy-vlog_tag.php');
            }
        }
        
        return $templates;
    }

    /**
     * Locate template file
     *
     * @param string $template_name Template name
     * @return string|false Template path or false if not found
     */
    public function locate_template($template_name) {
        // Check theme directory first
        $theme_template = locate_template($template_name);
        if ($theme_template) {
            return $theme_template;
        }

        // Check plugin templates directory
        $plugin_template = VLOG_PLUGIN_PATH . 'templates/' . $template_name;
        if (file_exists($plugin_template)) {
            return $plugin_template;
        }

        return false;
    }

    /**
     * Get template part
     *
     * @param string $slug Template slug
     * @param string $name Template name
     * @param array  $args Template arguments
     */
    public function get_template_part($slug, $name = '', $args = []): void {
        $templates = [];
        
        if ($name) {
            $templates[] = "{$slug}-{$name}.php";
        }
        
        $templates[] = "{$slug}.php";

        // Extract args to make them available in template
        if (!empty($args) && is_array($args)) {
            extract($args, EXTR_SKIP);
        }

        $template = $this->locate_template_part($templates);
        
        if ($template) {
            include $template;
        }
    }

    /**
     * Locate template part
     *
     * @param array $templates Template names
     * @return string|false Template path or false if not found
     */
    private function locate_template_part($templates) {
        foreach ($templates as $template) {
            // Check theme directory
            $theme_template = get_template_directory() . '/vlog-plugin/' . $template;
            if (file_exists($theme_template)) {
                return $theme_template;
            }

            // Check child theme directory
            if (is_child_theme()) {
                $child_template = get_stylesheet_directory() . '/vlog-plugin/' . $template;
                if (file_exists($child_template)) {
                    return $child_template;
                }
            }

            // Check plugin templates directory
            $plugin_template = VLOG_PLUGIN_PATH . 'templates/' . $template;
            if (file_exists($plugin_template)) {
                return $plugin_template;
            }
        }

        return false;
    }

    /**
     * Include template with args
     *
     * @param string $template_name Template name
     * @param array  $args          Template arguments
     * @return string Template output
     */
    public function get_template($template_name, $args = []): string {
        $template = $this->locate_template($template_name);
        
        if (!$template) {
            return '';
        }

        // Extract args to make them available in template
        if (!empty($args) && is_array($args)) {
            extract($args, EXTR_SKIP);
        }

        ob_start();
        include $template;
        return ob_get_clean();
    }

    /**
     * Load template with fallback
     *
     * @param string $template_name Template name
     * @param array  $args          Template arguments
     * @param string $fallback      Fallback content
     * @return string Template output
     */
    public function load_template($template_name, $args = [], $fallback = ''): string {
        $output = $this->get_template($template_name, $args);
        
        return $output ?: $fallback;
    }
}
