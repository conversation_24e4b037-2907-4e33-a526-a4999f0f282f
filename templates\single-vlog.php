<?php
/**
 * Single Vlog Template
 *
 * @package Anthropora\VlogPlugin
 */

get_header(); ?>

<div class="vlog-single-container">
    <?php while (have_posts()) : the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class('vlog-single-post'); ?>>
            <header class="vlog-single-header">
                <h1 class="vlog-single-title"><?php the_title(); ?></h1>
                
                <div class="vlog-single-meta">
                    <?php
                    $author = get_post_meta(get_the_ID(), '_vlog_author', true);
                    $published_at = get_post_meta(get_the_ID(), '_vlog_published_at', true);
                    $view_count = get_post_meta(get_the_ID(), '_vlog_view_count', true);
                    $platform = get_post_meta(get_the_ID(), '_vlog_platform', true);
                    ?>
                    
                    <?php if ($author): ?>
                        <span class="vlog-meta-author">
                            <strong><?php esc_html_e('By:', 'vlog-plugin'); ?></strong> <?php echo esc_html($author); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($published_at): ?>
                        <span class="vlog-meta-date">
                            <strong><?php esc_html_e('Published:', 'vlog-plugin'); ?></strong> 
                            <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($published_at))); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($view_count): ?>
                        <span class="vlog-meta-views">
                            <strong><?php esc_html_e('Views:', 'vlog-plugin'); ?></strong> 
                            <?php echo esc_html(number_format_i18n($view_count)); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($platform): ?>
                        <span class="vlog-meta-platform vlog-platform-<?php echo esc_attr($platform); ?>">
                            <?php echo esc_html(ucfirst($platform)); ?>
                        </span>
                    <?php endif; ?>
                </div>
                
                <?php
                // Display categories and tags
                $categories = get_the_terms(get_the_ID(), 'vlog_category');
                $tags = get_the_terms(get_the_ID(), 'vlog_tag');
                ?>
                
                <?php if ($categories && !is_wp_error($categories)): ?>
                    <div class="vlog-categories">
                        <strong><?php esc_html_e('Categories:', 'vlog-plugin'); ?></strong>
                        <?php foreach ($categories as $category): ?>
                            <a href="<?php echo esc_url(get_term_link($category)); ?>" class="vlog-category-link">
                                <?php echo esc_html($category->name); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($tags && !is_wp_error($tags)): ?>
                    <div class="vlog-tags">
                        <strong><?php esc_html_e('Tags:', 'vlog-plugin'); ?></strong>
                        <?php foreach ($tags as $tag): ?>
                            <a href="<?php echo esc_url(get_term_link($tag)); ?>" class="vlog-tag-link">
                                #<?php echo esc_html($tag->name); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </header>

            <div class="vlog-single-content">
                <?php the_content(); ?>
            </div>

            <?php if (has_excerpt()): ?>
                <div class="vlog-single-excerpt">
                    <h3><?php esc_html_e('Video Description', 'vlog-plugin'); ?></h3>
                    <?php the_excerpt(); ?>
                </div>
            <?php endif; ?>

            <footer class="vlog-single-footer">
                <?php
                // Navigation to next/previous videos
                $prev_post = get_previous_post(true, '', 'vlog_category');
                $next_post = get_next_post(true, '', 'vlog_category');
                ?>
                
                <?php if ($prev_post || $next_post): ?>
                    <nav class="vlog-navigation">
                        <h3><?php esc_html_e('More Videos', 'vlog-plugin'); ?></h3>
                        <div class="vlog-nav-links">
                            <?php if ($prev_post): ?>
                                <div class="vlog-nav-previous">
                                    <a href="<?php echo esc_url(get_permalink($prev_post)); ?>">
                                        <span class="vlog-nav-label"><?php esc_html_e('Previous Video', 'vlog-plugin'); ?></span>
                                        <span class="vlog-nav-title"><?php echo esc_html(get_the_title($prev_post)); ?></span>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($next_post): ?>
                                <div class="vlog-nav-next">
                                    <a href="<?php echo esc_url(get_permalink($next_post)); ?>">
                                        <span class="vlog-nav-label"><?php esc_html_e('Next Video', 'vlog-plugin'); ?></span>
                                        <span class="vlog-nav-title"><?php echo esc_html(get_the_title($next_post)); ?></span>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </nav>
                <?php endif; ?>
                
                <?php
                // Related videos from same category
                if ($categories && !is_wp_error($categories)) {
                    $category_ids = wp_list_pluck($categories, 'term_id');
                    
                    $related_args = [
                        'post_type' => 'vlog',
                        'post_status' => 'publish',
                        'posts_per_page' => 4,
                        'post__not_in' => [get_the_ID()],
                        'tax_query' => [
                            [
                                'taxonomy' => 'vlog_category',
                                'field' => 'term_id',
                                'terms' => $category_ids,
                            ],
                        ],
                        'meta_query' => [
                            [
                                'key' => '_vlog_embed_url',
                                'compare' => 'EXISTS',
                            ],
                        ],
                    ];
                    
                    $related_query = new WP_Query($related_args);
                    
                    if ($related_query->have_posts()):
                ?>
                    <div class="vlog-related-videos">
                        <h3><?php esc_html_e('Related Videos', 'vlog-plugin'); ?></h3>
                        <div class="vlog-related-grid">
                            <?php while ($related_query->have_posts()): $related_query->the_post(); ?>
                                <div class="vlog-related-item">
                                    <?php
                                    $thumbnail = get_post_meta(get_the_ID(), '_vlog_custom_thumbnail', true);
                                    if (!$thumbnail) {
                                        $thumbnail = get_post_meta(get_the_ID(), '_vlog_thumbnail_url', true);
                                    }
                                    ?>
                                    
                                    <a href="<?php the_permalink(); ?>" class="vlog-related-link">
                                        <?php if ($thumbnail): ?>
                                            <img src="<?php echo esc_url($thumbnail); ?>" 
                                                 alt="<?php echo esc_attr(get_the_title()); ?>"
                                                 loading="lazy" />
                                        <?php endif; ?>
                                        <h4><?php the_title(); ?></h4>
                                    </a>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                <?php
                    endif;
                    wp_reset_postdata();
                }
                ?>
            </footer>
        </article>

        <?php
        // Comments
        if (comments_open() || get_comments_number()) {
            comments_template();
        }
        ?>
    <?php endwhile; ?>
</div>

<?php get_footer(); ?>
