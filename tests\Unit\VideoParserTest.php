<?php
/**
 * Video Parser Tests
 *
 * @package Anthropora\VlogPlugin\Tests\Unit
 */

namespace Anthropora\VlogPlugin\Tests\Unit;

use Anthropora\VlogPlugin\Tests\TestCase;
use Anthropora\VlogPlugin\API\VideoParser;
use Anthropora\VlogPlugin\API\Parsers\YouTubeParser;
use Anthropora\VlogPlugin\API\Parsers\RumbleParser;

/**
 * Test video parsing functionality
 */
class VideoParserTest extends TestCase {

    /**
     * Video parser instance
     *
     * @var VideoParser
     */
    private $parser;

    /**
     * Set up test
     */
    public function setUp(): void {
        parent::setUp();
        $this->parser = new VideoParser();
    }

    /**
     * Test YouTube URL detection
     */
    public function testYouTubeUrlDetection(): void {
        $youtube_urls = [
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'https://youtu.be/dQw4w9WgXcQ',
            'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'https://youtube.com/watch?v=dQw4w9WgXcQ',
        ];

        foreach ($youtube_urls as $url) {
            $this->assertTrue($this->parser->is_supported_url($url), "Failed to detect YouTube URL: $url");
        }
    }

    /**
     * Test Rumble URL detection
     */
    public function testRumbleUrlDetection(): void {
        $rumble_urls = [
            'https://rumble.com/v123abc-test-video.html',
            'https://www.rumble.com/v123abc-test-video.html',
            'https://rumble.com/embed/v123abc/',
        ];

        foreach ($rumble_urls as $url) {
            $this->assertTrue($this->parser->is_supported_url($url), "Failed to detect Rumble URL: $url");
        }
    }

    /**
     * Test unsupported URL detection
     */
    public function testUnsupportedUrlDetection(): void {
        $unsupported_urls = [
            'https://vimeo.com/123456',
            'https://example.com/video',
            'not-a-url',
            '',
        ];

        foreach ($unsupported_urls as $url) {
            $this->assertFalse($this->parser->is_supported_url($url), "Incorrectly detected unsupported URL: $url");
        }
    }

    /**
     * Test YouTube video ID extraction
     */
    public function testYouTubeVideoIdExtraction(): void {
        $test_cases = [
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ' => 'dQw4w9WgXcQ',
            'https://youtu.be/dQw4w9WgXcQ' => 'dQw4w9WgXcQ',
            'https://www.youtube.com/embed/dQw4w9WgXcQ' => 'dQw4w9WgXcQ',
            'https://youtube.com/v/dQw4w9WgXcQ' => 'dQw4w9WgXcQ',
        ];

        foreach ($test_cases as $url => $expected_id) {
            $actual_id = $this->parser->get_video_id($url);
            $this->assertEquals($expected_id, $actual_id, "Failed to extract video ID from: $url");
        }
    }

    /**
     * Test Rumble video ID extraction
     */
    public function testRumbleVideoIdExtraction(): void {
        $test_cases = [
            'https://rumble.com/v123abc-test-video.html' => '123abc',
            'https://rumble.com/embed/v123abc/' => '123abc',
        ];

        foreach ($test_cases as $url => $expected_id) {
            $actual_id = $this->parser->get_video_id($url);
            $this->assertEquals($expected_id, $actual_id, "Failed to extract video ID from: $url");
        }
    }

    /**
     * Test YouTube video parsing with API
     */
    public function testYouTubeVideoParsingWithApi(): void {
        // Mock HTTP request
        $this->mockHttpRequest(
            'https://www.googleapis.com/youtube/v3/videos?part=snippet%2CcontentDetails%2Cstatistics&id=dQw4w9WgXcQ&key=test-key',
            $this->getSampleYouTubeResponse()
        );

        // Set API key
        update_option('vlog_plugin_options', [
            'youtube_api_key' => 'test-key'
        ]);

        $video_data = $this->parser->parse_video_url('https://www.youtube.com/watch?v=dQw4w9WgXcQ');

        $this->assertValidVideoData($video_data);
        $this->assertEquals('youtube', $video_data['platform']);
        $this->assertEquals('dQw4w9WgXcQ', $video_data['video_id']);
        $this->assertEquals('Test Video Title', $video_data['title']);
    }

    /**
     * Test YouTube video parsing with oEmbed fallback
     */
    public function testYouTubeVideoParsingWithOEmbed(): void {
        // Mock oEmbed request
        $this->mockHttpRequest(
            'https://www.youtube.com/oembed?url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DdQw4w9WgXcQ&format=json',
            [
                'response' => ['code' => 200],
                'body' => json_encode([
                    'title' => 'Test Video Title',
                    'author_name' => 'Test Channel',
                    'thumbnail_url' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
                ])
            ]
        );

        $video_data = $this->parser->parse_video_url('https://www.youtube.com/watch?v=dQw4w9WgXcQ');

        $this->assertValidVideoData($video_data);
        $this->assertEquals('youtube', $video_data['platform']);
        $this->assertEquals('Test Video Title', $video_data['title']);
    }

    /**
     * Test Rumble video parsing
     */
    public function testRumbleVideoParsing(): void {
        // Mock HTTP request
        $this->mockHttpRequest(
            'https://rumble.com/v123abc-test-video.html',
            $this->getSampleRumbleResponse()
        );

        $video_data = $this->parser->parse_video_url('https://rumble.com/v123abc-test-video.html');

        $this->assertValidVideoData($video_data);
        $this->assertEquals('rumble', $video_data['platform']);
        $this->assertEquals('123abc', $video_data['video_id']);
        $this->assertEquals('Test Video', $video_data['title']);
    }

    /**
     * Test batch parsing
     */
    public function testBatchParsing(): void {
        // Mock requests
        $this->mockHttpRequest(
            'https://www.youtube.com/oembed?url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DdQw4w9WgXcQ&format=json',
            [
                'response' => ['code' => 200],
                'body' => json_encode(['title' => 'Video 1', 'author_name' => 'Author 1'])
            ]
        );

        $this->mockHttpRequest(
            'https://rumble.com/v123abc-test.html',
            [
                'response' => ['code' => 200],
                'body' => '<title>Video 2 - Rumble</title><meta property="og:title" content="Video 2">'
            ]
        );

        $urls = [
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'https://rumble.com/v123abc-test.html',
            'https://invalid-url.com/video'
        ];

        $results = $this->parser->batch_parse($urls);

        $this->assertCount(2, $results); // Only valid URLs should be parsed
        $this->assertEquals('Video 1', $results[0]['title']);
        $this->assertEquals('Video 2', $results[1]['title']);
    }

    /**
     * Test invalid URL handling
     */
    public function testInvalidUrlHandling(): void {
        $invalid_urls = [
            '',
            'not-a-url',
            'https://example.com/not-a-video',
            'javascript:alert("xss")',
        ];

        foreach ($invalid_urls as $url) {
            $result = $this->parser->parse_video_url($url);
            $this->assertFalse($result, "Should return false for invalid URL: $url");
        }
    }

    /**
     * Test supported platforms
     */
    public function testSupportedPlatforms(): void {
        $platforms = $this->parser->get_supported_platforms();
        
        $this->assertIsArray($platforms);
        $this->assertContains('youtube', $platforms);
        $this->assertContains('rumble', $platforms);
    }

    /**
     * Test YouTube parser directly
     */
    public function testYouTubeParserDirectly(): void {
        $parser = new YouTubeParser();
        
        $this->assertTrue($parser->can_parse('https://www.youtube.com/watch?v=test'));
        $this->assertFalse($parser->can_parse('https://rumble.com/vtest'));
        
        $this->assertEquals('test123', $parser->get_video_id('https://www.youtube.com/watch?v=test123'));
        $this->assertEquals('https://www.youtube.com/embed/test123', $parser->get_embed_url('test123'));
    }

    /**
     * Test Rumble parser directly
     */
    public function testRumbleParserDirectly(): void {
        $parser = new RumbleParser();
        
        $this->assertTrue($parser->can_parse('https://rumble.com/vtest-video.html'));
        $this->assertFalse($parser->can_parse('https://www.youtube.com/watch?v=test'));
        
        $this->assertEquals('test123', $parser->get_video_id('https://rumble.com/vtest123-video.html'));
        $this->assertEquals('https://rumble.com/embed/vtest123/', $parser->get_embed_url('test123'));
    }

    /**
     * Test error handling for API failures
     */
    public function testApiFailureHandling(): void {
        // Mock failed API request
        $this->mockHttpRequest(
            'https://www.googleapis.com/youtube/v3/videos?part=snippet%2CcontentDetails%2Cstatistics&id=dQw4w9WgXcQ&key=test-key',
            new \WP_Error('http_request_failed', 'Request failed')
        );

        // Set API key
        update_option('vlog_plugin_options', [
            'youtube_api_key' => 'test-key'
        ]);

        // Should fallback to oEmbed
        $this->mockHttpRequest(
            'https://www.youtube.com/oembed?url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DdQw4w9WgXcQ&format=json',
            [
                'response' => ['code' => 200],
                'body' => json_encode(['title' => 'Fallback Title'])
            ]
        );

        $video_data = $this->parser->parse_video_url('https://www.youtube.com/watch?v=dQw4w9WgXcQ');

        $this->assertValidVideoData($video_data);
        $this->assertEquals('Fallback Title', $video_data['title']);
    }
}
