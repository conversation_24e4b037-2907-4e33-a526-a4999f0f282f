<?php
/**
 * Frontend Manager
 *
 * @package Anthropora\VlogPlugin\Frontend
 */

namespace Anthropora\VlogPlugin\Frontend;

use Anthropora\VlogPlugin\Database\DatabaseManager;

/**
 * Handles frontend functionality
 */
class FrontendManager {

    /**
     * Database manager instance
     *
     * @var DatabaseManager
     */
    private $database_manager;

    /**
     * Shortcodes instance
     *
     * @var Shortcodes
     */
    private $shortcodes;

    /**
     * Template loader instance
     *
     * @var TemplateLoader
     */
    private $template_loader;

    /**
     * Menu manager instance
     *
     * @var MenuManager
     */
    private $menu_manager;

    /**
     * Constructor
     *
     * @param DatabaseManager $database_manager Database manager
     */
    public function __construct(DatabaseManager $database_manager) {
        $this->database_manager = $database_manager;
        $this->shortcodes = new Shortcodes();
        $this->template_loader = new TemplateLoader();
        $this->menu_manager = new MenuManager();
    }

    /**
     * Initialize frontend functionality
     */
    public function init(): void {
        $this->shortcodes->init();
        $this->template_loader->init();
        $this->menu_manager->init();
        
        add_action('wp_head', [$this, 'add_structured_data']);
        add_filter('the_content', [$this, 'add_video_player_to_content']);
        add_action('wp_ajax_load_more_videos', [$this, 'ajax_load_more_videos']);
        add_action('wp_ajax_nopriv_load_more_videos', [$this, 'ajax_load_more_videos']);
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts(): void {
        if ($this->should_enqueue_assets()) {
            wp_enqueue_style(
                'vlog-frontend-style',
                VLOG_PLUGIN_URL . 'assets/css/frontend.css',
                [],
                VLOG_PLUGIN_VERSION
            );

            wp_enqueue_script(
                'vlog-frontend-script',
                VLOG_PLUGIN_URL . 'assets/js/frontend.js',
                ['jquery'],
                VLOG_PLUGIN_VERSION,
                true
            );

            $options = get_option('vlog_plugin_options', []);
            
            wp_localize_script('vlog-frontend-script', 'vlogFrontend', [
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('vlog_frontend_nonce'),
                'enableLightbox' => $options['enable_lightbox'] ?? true,
                'strings' => [
                    'loadMore' => __('Load More', 'vlog-plugin'),
                    'loading' => __('Loading...', 'vlog-plugin'),
                    'noMoreVideos' => __('No more videos to load.', 'vlog-plugin'),
                ],
            ]);

            // Enqueue lightbox if enabled
            if ($options['enable_lightbox'] ?? true) {
                wp_enqueue_script(
                    'vlog-lightbox',
                    VLOG_PLUGIN_URL . 'assets/js/lightbox.js',
                    ['jquery'],
                    VLOG_PLUGIN_VERSION,
                    true
                );
            }
        }
    }

    /**
     * Add structured data for videos
     */
    public function add_structured_data(): void {
        if (is_singular('vlog')) {
            global $post;
            
            $video_data = $this->get_video_structured_data($post->ID);
            
            if ($video_data) {
                echo '<script type="application/ld+json">' . wp_json_encode($video_data, JSON_UNESCAPED_SLASHES) . '</script>';
            }
        }
    }

    /**
     * Add video player to single vlog content
     *
     * @param string $content Post content
     * @return string Modified content
     */
    public function add_video_player_to_content($content): string {
        if (is_singular('vlog') && in_the_loop() && is_main_query()) {
            global $post;
            
            $embed_url = get_post_meta($post->ID, '_vlog_embed_url', true);
            
            if ($embed_url) {
                $video_player = $this->render_video_player($post->ID);
                $content = $video_player . $content;
            }
        }

        return $content;
    }

    /**
     * AJAX handler for loading more videos
     */
    public function ajax_load_more_videos(): void {
        check_ajax_referer('vlog_frontend_nonce', 'nonce');

        $page = intval($_POST['page'] ?? 1);
        $category = sanitize_text_field($_POST['category'] ?? '');
        $posts_per_page = intval($_POST['posts_per_page'] ?? 12);

        $args = [
            'post_type' => 'vlog',
            'post_status' => 'publish',
            'posts_per_page' => $posts_per_page,
            'paged' => $page,
            'meta_query' => [
                [
                    'key' => '_vlog_embed_url',
                    'compare' => 'EXISTS',
                ],
            ],
        ];

        if (!empty($category)) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'vlog_category',
                    'field' => 'slug',
                    'terms' => $category,
                ],
            ];
        }

        $query = new \WP_Query($args);
        
        if ($query->have_posts()) {
            ob_start();
            
            while ($query->have_posts()) {
                $query->the_post();
                $this->template_loader->get_template_part('gallery', 'item');
            }
            
            wp_reset_postdata();
            
            $html = ob_get_clean();
            
            wp_send_json_success([
                'html' => $html,
                'has_more' => $page < $query->max_num_pages,
            ]);
        } else {
            wp_send_json_success([
                'html' => '',
                'has_more' => false,
            ]);
        }
    }

    /**
     * Check if assets should be enqueued
     *
     * @return bool
     */
    private function should_enqueue_assets(): bool {
        global $post;

        // Always enqueue on vlog pages
        if (is_singular('vlog') || is_post_type_archive('vlog') || is_tax(['vlog_category', 'vlog_tag'])) {
            return true;
        }

        // Check if page contains vlog shortcodes
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'vlog_gallery')) {
            return true;
        }

        // Check if it's the main vlog page
        $main_page_id = get_option('vlog_plugin_main_page_id');
        if ($main_page_id && is_page($main_page_id)) {
            return true;
        }

        return false;
    }

    /**
     * Get structured data for video
     *
     * @param int $post_id Post ID
     * @return array|null Structured data or null
     */
    private function get_video_structured_data($post_id): ?array {
        $embed_url = get_post_meta($post_id, '_vlog_embed_url', true);
        $thumbnail = get_post_meta($post_id, '_vlog_thumbnail_url', true);
        $duration = get_post_meta($post_id, '_vlog_duration', true);
        $author = get_post_meta($post_id, '_vlog_author', true);
        $published_at = get_post_meta($post_id, '_vlog_published_at', true);

        if (!$embed_url) {
            return null;
        }

        $post = get_post($post_id);
        
        $structured_data = [
            '@context' => 'https://schema.org',
            '@type' => 'VideoObject',
            'name' => $post->post_title,
            'description' => $post->post_excerpt ?: wp_trim_words($post->post_content, 30),
            'embedUrl' => $embed_url,
            'url' => get_permalink($post_id),
        ];

        if ($thumbnail) {
            $structured_data['thumbnailUrl'] = $thumbnail;
        }

        if ($duration) {
            $structured_data['duration'] = 'PT' . $duration . 'S';
        }

        if ($author) {
            $structured_data['author'] = [
                '@type' => 'Person',
                'name' => $author,
            ];
        }

        if ($published_at) {
            $structured_data['uploadDate'] = date('c', strtotime($published_at));
        }

        return $structured_data;
    }

    /**
     * Render video player
     *
     * @param int $post_id Post ID
     * @return string Video player HTML
     */
    private function render_video_player($post_id): string {
        $embed_url = get_post_meta($post_id, '_vlog_embed_url', true);
        $autoplay = get_post_meta($post_id, '_vlog_autoplay', true);
        $show_controls = get_post_meta($post_id, '_vlog_show_controls', true);

        if (!$embed_url) {
            return '';
        }

        // Add parameters to embed URL
        $url_params = [];
        
        if ($autoplay) {
            $url_params['autoplay'] = '1';
        }
        
        if (!$show_controls) {
            $url_params['controls'] = '0';
        }

        if (!empty($url_params)) {
            $embed_url = add_query_arg($url_params, $embed_url);
        }

        ob_start();
        ?>
        <div class="vlog-video-player">
            <div class="vlog-video-wrapper">
                <iframe 
                    src="<?php echo esc_url($embed_url); ?>"
                    frameborder="0"
                    allowfullscreen
                    loading="lazy"
                    title="<?php echo esc_attr(get_the_title($post_id)); ?>">
                </iframe>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
