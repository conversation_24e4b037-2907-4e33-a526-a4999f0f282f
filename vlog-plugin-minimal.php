<?php
/**
 * Plugin Name: Professional Vlog Plugin (Minimal)
 * Plugin URI: https://anthropora.com/vlog-plugin
 * Description: A minimal version for testing - WordPress plugin for managing video blogs.
 * Version: 1.0.0
 * Author: Anthropora
 * License: GPL v2 or later
 * Text Domain: vlog-plugin
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('VLOG_PLUGIN_VERSION', '1.0.0');
define('VLOG_PLUGIN_FILE', __FILE__);
define('VLOG_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('VLOG_PLUGIN_URL', plugin_dir_url(__FILE__));

// Simple activation hook
register_activation_hook(__FILE__, 'vlog_plugin_activate');
function vlog_plugin_activate() {
    // Create the vlog post type
    vlog_register_post_type();
    
    // Flush rewrite rules
    flush_rewrite_rules();
    
    // Create default page
    $page_title = __('Video Blog', 'vlog-plugin');
    $page_slug = 'video-blog';
    
    $existing_page = get_page_by_path($page_slug);
    if (!$existing_page) {
        $page_id = wp_insert_post([
            'post_title'   => $page_title,
            'post_name'    => $page_slug,
            'post_content' => '[vlog_gallery]',
            'post_status'  => 'publish',
            'post_type'    => 'page',
            'post_author'  => get_current_user_id(),
        ]);
        
        if ($page_id && !is_wp_error($page_id)) {
            update_option('vlog_plugin_main_page_id', $page_id);
        }
    }
}

// Register post type
add_action('init', 'vlog_register_post_type');
function vlog_register_post_type() {
    $labels = [
        'name'                  => _x('Vlogs', 'Post type general name', 'vlog-plugin'),
        'singular_name'         => _x('Vlog', 'Post type singular name', 'vlog-plugin'),
        'menu_name'             => _x('Vlogs', 'Admin Menu text', 'vlog-plugin'),
        'add_new'               => __('Add New', 'vlog-plugin'),
        'add_new_item'          => __('Add New Vlog', 'vlog-plugin'),
        'edit_item'             => __('Edit Vlog', 'vlog-plugin'),
        'view_item'             => __('View Vlog', 'vlog-plugin'),
        'all_items'             => __('All Vlogs', 'vlog-plugin'),
    ];

    $args = [
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'show_in_rest'       => true,
        'query_var'          => true,
        'rewrite'            => ['slug' => 'vlog'],
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 20,
        'menu_icon'          => 'dashicons-video-alt3',
        'supports'           => ['title', 'editor', 'thumbnail', 'excerpt'],
    ];

    register_post_type('vlog', $args);
}

// Register taxonomy
add_action('init', 'vlog_register_taxonomy');
function vlog_register_taxonomy() {
    $labels = [
        'name'              => _x('Vlog Categories', 'taxonomy general name', 'vlog-plugin'),
        'singular_name'     => _x('Vlog Category', 'taxonomy singular name', 'vlog-plugin'),
        'search_items'      => __('Search Categories', 'vlog-plugin'),
        'all_items'         => __('All Categories', 'vlog-plugin'),
        'edit_item'         => __('Edit Category', 'vlog-plugin'),
        'update_item'       => __('Update Category', 'vlog-plugin'),
        'add_new_item'      => __('Add New Category', 'vlog-plugin'),
        'new_item_name'     => __('New Category Name', 'vlog-plugin'),
        'menu_name'         => __('Categories', 'vlog-plugin'),
    ];

    $args = [
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => ['slug' => 'vlog-category'],
        'show_in_rest'      => true,
    ];

    register_taxonomy('vlog_category', ['vlog'], $args);
}

// Add meta box for video URL
add_action('add_meta_boxes', 'vlog_add_meta_boxes');
function vlog_add_meta_boxes() {
    add_meta_box(
        'vlog_video_url',
        __('Video URL', 'vlog-plugin'),
        'vlog_video_url_callback',
        'vlog',
        'normal',
        'high'
    );
}

function vlog_video_url_callback($post) {
    wp_nonce_field('vlog_video_url_nonce', 'vlog_video_url_nonce');
    $video_url = get_post_meta($post->ID, '_vlog_video_url', true);
    ?>
    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="vlog_video_url"><?php esc_html_e('Video URL', 'vlog-plugin'); ?></label>
            </th>
            <td>
                <input type="url" id="vlog_video_url" name="vlog_video_url" 
                       value="<?php echo esc_attr($video_url); ?>" 
                       class="regular-text" placeholder="https://www.youtube.com/watch?v=..." />
                <p class="description">
                    <?php esc_html_e('Enter a YouTube or Rumble video URL.', 'vlog-plugin'); ?>
                </p>
            </td>
        </tr>
    </table>
    <?php
}

// Save meta box data
add_action('save_post', 'vlog_save_meta_boxes');
function vlog_save_meta_boxes($post_id) {
    if (!isset($_POST['vlog_video_url_nonce']) || !wp_verify_nonce($_POST['vlog_video_url_nonce'], 'vlog_video_url_nonce')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (get_post_type($post_id) !== 'vlog') {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    if (isset($_POST['vlog_video_url'])) {
        update_post_meta($post_id, '_vlog_video_url', esc_url_raw($_POST['vlog_video_url']));
    }
}

// Simple shortcode
add_shortcode('vlog_gallery', 'vlog_gallery_shortcode');
function vlog_gallery_shortcode($atts) {
    $atts = shortcode_atts([
        'posts_per_page' => 12,
        'columns' => 3,
    ], $atts, 'vlog_gallery');

    $query = new WP_Query([
        'post_type' => 'vlog',
        'post_status' => 'publish',
        'posts_per_page' => intval($atts['posts_per_page']),
    ]);

    if (!$query->have_posts()) {
        return '<p>' . esc_html__('No videos found.', 'vlog-plugin') . '</p>';
    }

    ob_start();
    ?>
    <div class="vlog-gallery vlog-columns-<?php echo esc_attr($atts['columns']); ?>">
        <div class="vlog-gallery-grid">
            <?php while ($query->have_posts()): $query->the_post(); ?>
                <div class="vlog-gallery-item">
                    <div class="vlog-item-content">
                        <h3 class="vlog-item-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        <?php if (has_excerpt()): ?>
                            <div class="vlog-item-excerpt">
                                <?php the_excerpt(); ?>
                            </div>
                        <?php endif; ?>
                        <?php
                        $video_url = get_post_meta(get_the_ID(), '_vlog_video_url', true);
                        if ($video_url):
                        ?>
                            <p><a href="<?php echo esc_url($video_url); ?>" target="_blank">
                                <?php esc_html_e('Watch Video', 'vlog-plugin'); ?>
                            </a></p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    </div>
    
    <style>
    .vlog-gallery-grid {
        display: grid;
        gap: 20px;
        margin: 20px 0;
    }
    .vlog-columns-1 .vlog-gallery-grid { grid-template-columns: 1fr; }
    .vlog-columns-2 .vlog-gallery-grid { grid-template-columns: repeat(2, 1fr); }
    .vlog-columns-3 .vlog-gallery-grid { grid-template-columns: repeat(3, 1fr); }
    .vlog-columns-4 .vlog-gallery-grid { grid-template-columns: repeat(4, 1fr); }
    
    .vlog-gallery-item {
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .vlog-item-title {
        margin: 0 0 8px 0;
        font-size: 16px;
    }
    
    .vlog-item-title a {
        color: #333;
        text-decoration: none;
    }
    
    .vlog-item-title a:hover {
        color: #0073aa;
    }
    
    @media (max-width: 768px) {
        .vlog-columns-2 .vlog-gallery-grid,
        .vlog-columns-3 .vlog-gallery-grid,
        .vlog-columns-4 .vlog-gallery-grid {
            grid-template-columns: 1fr;
        }
    }
    </style>
    <?php
    wp_reset_postdata();
    return ob_get_clean();
}

// Admin notice for successful activation
add_action('admin_notices', 'vlog_plugin_activation_notice');
function vlog_plugin_activation_notice() {
    if (get_transient('vlog_plugin_activated')) {
        delete_transient('vlog_plugin_activated');
        ?>
        <div class="notice notice-success is-dismissible">
            <p><?php esc_html_e('Vlog Plugin activated successfully! You can now add videos under "Vlogs" in the admin menu.', 'vlog-plugin'); ?></p>
        </div>
        <?php
    }
}

// Set activation notice
register_activation_hook(__FILE__, function() {
    set_transient('vlog_plugin_activated', true, 30);
});

// Deactivation hook
register_deactivation_hook(__FILE__, 'vlog_plugin_deactivate');
function vlog_plugin_deactivate() {
    flush_rewrite_rules();
}
