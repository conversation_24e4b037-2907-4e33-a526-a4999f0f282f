# Professional Vlog Plugin

A comprehensive WordPress plugin for managing video blogs with YouTube and Rumble integration, featuring video galleries, categories, and pagination.

## Features

### 🎥 Video Management
- **Multi-Platform Support**: YouTube and Rumble video integration
- **Automatic Metadata Extraction**: Titles, descriptions, thumbnails, and duration
- **Custom Post Type**: Dedicated vlog post type with rich metadata
- **Category System**: Hierarchical categories for video organization
- **Featured Videos**: Mark videos as featured for special display

### 🎨 Frontend Display
- **Responsive Video Galleries**: Grid-based layouts with multiple column options
- **Lightbox Integration**: Modal video playback with autoplay
- **Pagination**: Built-in pagination and load-more functionality
- **Category Filtering**: Dynamic filtering by video categories
- **Template System**: Customizable templates with theme override support

### 🔧 Admin Interface
- **Intuitive Management**: WordPress-native admin interface
- **Bulk Operations**: Import multiple videos, bulk edit features
- **Video Parser**: One-click video URL parsing and metadata extraction
- **Settings Panel**: Comprehensive configuration options
- **Import Tool**: Batch import videos from URLs

### 🚀 Performance & Security
- **Caching System**: Multi-layer caching for optimal performance
- **Security Hardened**: Input sanitization, nonce verification, rate limiting
- **SEO Optimized**: Structured data, meta tags, and clean URLs
- **Mobile Responsive**: Optimized for all device sizes

## Installation

### Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

### Manual Installation
1. Download the plugin files
2. Upload to `/wp-content/plugins/vlog-plugin/`
3. Activate through the WordPress admin panel
4. Configure settings under **Vlogs > Settings**

### Composer Installation
```bash
composer require anthropora/vlog-plugin
```

## Configuration

### Basic Setup
1. Navigate to **Vlogs > Settings**
2. Configure video platforms (YouTube, Rumble)
3. Set display preferences
4. Configure caching options

### YouTube API Setup (Optional)
1. Create a Google Cloud Project
2. Enable YouTube Data API v3
3. Generate an API key
4. Add the key in plugin settings for enhanced metadata

### Menu Integration
The plugin automatically adds a "Video Blog" page to your site and can integrate with your navigation menu.

## Usage

### Adding Videos

#### Single Video
1. Go to **Vlogs > Add New**
2. Enter video URL in the Video Details meta box
3. Click "Parse Video" to extract metadata
4. Assign categories and publish

#### Bulk Import
1. Navigate to **Vlogs > Import**
2. Paste video URLs (one per line)
3. Select category (optional)
4. Click "Import Videos"

### Shortcodes

#### Video Gallery
```php
[vlog_gallery posts_per_page="12" columns="3" category="tech"]
```

**Attributes:**
- `posts_per_page`: Number of videos per page (default: 12)
- `columns`: Grid columns (1-4, default: 3)
- `category`: Filter by category slug
- `tag`: Filter by tag slug
- `orderby`: Sort order (date, title, etc.)
- `order`: ASC or DESC
- `show_pagination`: Enable pagination (true/false)
- `show_filters`: Show category filters (true/false)
- `featured_only`: Show only featured videos (true/false)

#### Single Video
```php
[vlog_video id="123"]
[vlog_video url="https://www.youtube.com/watch?v=dQw4w9WgXcQ"]
```

#### Category List
```php
[vlog_categories style="list" show_count="true"]
```

#### Featured Videos
```php
[vlog_featured limit="6" columns="3"]
```

### Template Customization

#### Theme Override
Copy templates to your theme directory:
```
your-theme/
├── vlog-plugin/
│   ├── single-vlog.php
│   ├── archive-vlog.php
│   ├── taxonomy-vlog_category.php
│   └── gallery-item.php
```

#### Template Hierarchy
- `single-vlog.php` - Single video page
- `archive-vlog.php` - Video archive page
- `taxonomy-vlog_category.php` - Category archive
- `taxonomy-vlog_tag.php` - Tag archive

## Development

### Setup Development Environment
```bash
# Clone repository
git clone https://github.com/anthropora/vlog-plugin.git
cd vlog-plugin

# Install dependencies
composer install
npm install

# Run tests
composer test

# Code quality checks
composer phpcs
composer phpstan
```

### File Structure
```
vlog-plugin/
├── src/                    # PHP source code
│   ├── Admin/             # Admin interface
│   ├── API/               # Video parsers
│   ├── Core/              # Core functionality
│   ├── Database/          # Database operations
│   └── Frontend/          # Frontend display
├── assets/                # CSS/JS assets
├── templates/             # Template files
├── tests/                 # Test suite
├── languages/             # Translation files
└── vendor/                # Composer dependencies
```

### Hooks & Filters

#### Actions
- `vlog_plugin_activated` - Plugin activation
- `vlog_plugin_deactivated` - Plugin deactivation
- `vlog_video_imported` - After video import
- `vlog_cache_cleared` - Cache cleared

#### Filters
- `vlog_plugin_video_data` - Modify parsed video data
- `vlog_plugin_gallery_args` - Modify gallery query args
- `vlog_plugin_allowed_domains` - Add allowed video domains
- `vlog_plugin_cache_duration` - Modify cache duration

### Custom Post Type
- **Post Type**: `vlog`
- **Taxonomies**: `vlog_category`, `vlog_tag`
- **Meta Fields**: Video URL, platform, duration, etc.

## API Reference

### VideoParser Class
```php
use Anthropora\VlogPlugin\API\VideoParser;

$parser = new VideoParser();
$video_data = $parser->parse_video_url($url);
```

### Cache Class
```php
use Anthropora\VlogPlugin\Core\Cache;

Cache::set('key', $data, 3600);
$data = Cache::get('key');
```

### Security Class
```php
use Anthropora\VlogPlugin\Core\Security;

$clean_url = Security::validate_video_url($url);
$sanitized = Security::sanitize_video_data($data);
```

## Troubleshooting

### Common Issues

#### Videos Not Loading
1. Check video URL format
2. Verify platform is enabled in settings
3. Clear plugin cache
4. Check browser console for errors

#### Import Failures
1. Ensure URLs are publicly accessible
2. Check rate limiting settings
3. Verify user permissions
4. Review error logs

#### Performance Issues
1. Enable caching in settings
2. Optimize database queries
3. Use CDN for video thumbnails
4. Check server resources

### Debug Mode
Enable WordPress debug mode and check logs:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Contributing

### Bug Reports
Please use the GitHub issue tracker for bug reports and feature requests.

### Pull Requests
1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit pull request

### Coding Standards
- Follow WordPress Coding Standards
- Use PSR-4 autoloading
- Write comprehensive tests
- Document all public methods

## License

This plugin is licensed under the GPL v2 or later.

## Support

- **Documentation**: [Plugin Documentation](https://anthropora.com/vlog-plugin/docs)
- **Support Forum**: [WordPress.org Support](https://wordpress.org/support/plugin/vlog-plugin)
- **GitHub Issues**: [Report Issues](https://github.com/anthropora/vlog-plugin/issues)

## Changelog

### 1.0.0
- Initial release
- YouTube and Rumble integration
- Video gallery system
- Admin interface
- Caching and security features
- Comprehensive test suite

---

**Developed by [Anthropora](https://anthropora.com)** - Professional WordPress Development
