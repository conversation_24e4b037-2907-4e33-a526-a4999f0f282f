<?php
/**
 * Plugin Uninstaller
 *
 * @package Anthropora\VlogPlugin
 */

namespace Anthropora\VlogPlugin;

/**
 * Handles plugin uninstallation
 */
class Uninstaller {

    /**
     * Uninstall the plugin
     */
    public static function uninstall(): void {
        // Check if user has permission to delete plugins
        if (!current_user_can('delete_plugins')) {
            return;
        }

        // Remove plugin options
        self::remove_options();

        // Remove custom tables (optional - ask user)
        if (get_option('vlog_plugin_remove_data_on_uninstall', false)) {
            self::remove_database_tables();
            self::remove_posts_and_terms();
        }

        // Clear all cached data
        self::clear_all_cache();
    }

    /**
     * Remove plugin options
     */
    private static function remove_options(): void {
        delete_option('vlog_plugin_options');
        delete_option('vlog_plugin_version');
        delete_option('vlog_plugin_main_page_id');
        delete_option('vlog_plugin_remove_data_on_uninstall');
    }

    /**
     * Remove custom database tables
     */
    private static function remove_database_tables(): void {
        global $wpdb;

        $table_name = $wpdb->prefix . 'vlog_video_cache';
        $wpdb->query("DROP TABLE IF EXISTS {$table_name}");
    }

    /**
     * Remove custom posts and terms
     */
    private static function remove_posts_and_terms(): void {
        // Remove all vlog posts
        $posts = get_posts([
            'post_type' => 'vlog',
            'numberposts' => -1,
            'post_status' => 'any',
        ]);

        foreach ($posts as $post) {
            wp_delete_post($post->ID, true);
        }

        // Remove vlog categories
        $terms = get_terms([
            'taxonomy' => 'vlog_category',
            'hide_empty' => false,
        ]);

        foreach ($terms as $term) {
            wp_delete_term($term->term_id, 'vlog_category');
        }
    }

    /**
     * Clear all plugin cache
     */
    private static function clear_all_cache(): void {
        global $wpdb;

        // Clear transients
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
                '_transient_vlog_plugin_%',
                '_transient_timeout_vlog_plugin_%'
            )
        );

        // Clear object cache if available
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
    }
}
