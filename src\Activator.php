<?php
/**
 * Plugin Activator
 *
 * @package Anthropora\VlogPlugin
 */

namespace Anthropora\VlogPlugin;

use Anthropora\VlogPlugin\Database\DatabaseManager;

/**
 * Handles plugin activation
 */
class Activator {

    /**
     * Activate the plugin
     */
    public static function activate(): void {
        // Check WordPress version
        if (version_compare(get_bloginfo('version'), '5.0', '<')) {
            wp_die(
                esc_html__('This plugin requires WordPress version 5.0 or higher.', 'vlog-plugin'),
                esc_html__('Plugin Activation Error', 'vlog-plugin'),
                ['back_link' => true]
            );
        }

        // Check PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            wp_die(
                esc_html__('This plugin requires PHP version 7.4 or higher.', 'vlog-plugin'),
                esc_html__('Plugin Activation Error', 'vlog-plugin'),
                ['back_link' => true]
            );
        }

        // Create database tables
        $database_manager = new DatabaseManager();
        $database_manager->create_tables();

        // Register post types and taxonomies
        $database_manager->register_post_types();
        $database_manager->register_taxonomies();

        // Flush rewrite rules
        flush_rewrite_rules();

        // Create default pages
        self::create_default_pages();

        // Set default options
        self::set_default_options();

        // Schedule cron events
        self::schedule_cron_events();
    }

    /**
     * Create default pages
     */
    private static function create_default_pages(): void {
        // Create Video Blog page if it doesn't exist
        $page_title = __('Video Blog', 'vlog-plugin');
        $page_slug = 'video-blog';

        $existing_page = get_page_by_path($page_slug);
        if (!$existing_page) {
            $page_id = wp_insert_post([
                'post_title'   => $page_title,
                'post_name'    => $page_slug,
                'post_content' => '[vlog_gallery]',
                'post_status'  => 'publish',
                'post_type'    => 'page',
                'post_author'  => get_current_user_id(),
            ]);

            if ($page_id && !is_wp_error($page_id)) {
                update_option('vlog_plugin_main_page_id', $page_id);
            }
        }
    }

    /**
     * Set default plugin options
     */
    private static function set_default_options(): void {
        $default_options = [
            'videos_per_page' => 12,
            'enable_youtube' => true,
            'enable_rumble' => true,
            'cache_duration' => 3600, // 1 hour
            'thumbnail_size' => 'medium',
            'enable_lightbox' => true,
            'show_video_duration' => true,
            'show_video_date' => true,
        ];

        add_option('vlog_plugin_options', $default_options);
        add_option('vlog_plugin_version', VLOG_PLUGIN_VERSION);
    }

    /**
     * Schedule cron events
     */
    private static function schedule_cron_events(): void {
        if (!wp_next_scheduled('vlog_plugin_cleanup_cache')) {
            wp_schedule_event(time(), 'daily', 'vlog_plugin_cleanup_cache');
        }
    }
}
