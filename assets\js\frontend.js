/**
 * Frontend JavaScript for Vlog Plugin
 */

(function($) {
    'use strict';

    // Initialize when DOM is ready
    $(document).ready(function() {
        VlogPlugin.init();
    });

    // Main plugin object
    window.VlogPlugin = {
        
        // Configuration
        config: {
            lightboxEnabled: vlogFrontend.enableLightbox || true,
            ajaxUrl: vlogFrontend.ajaxUrl,
            nonce: vlogFrontend.nonce,
            strings: vlogFrontend.strings || {}
        },

        // Initialize plugin
        init: function() {
            this.bindEvents();
            this.initLightbox();
            this.initLoadMore();
            this.initFilters();
            this.initLazyLoading();
        },

        // Bind event handlers
        bindEvents: function() {
            var self = this;

            // Play button clicks
            $(document).on('click', '.vlog-play-button', function(e) {
                e.preventDefault();
                var embedUrl = $(this).data('embed-url');
                if (embedUrl && self.config.lightboxEnabled) {
                    self.openLightbox(embedUrl);
                } else {
                    // Fallback to direct link
                    var $item = $(this).closest('.vlog-gallery-item, .vlog-archive-item');
                    var link = $item.find('.vlog-item-title a').attr('href');
                    if (link) {
                        window.location.href = link;
                    }
                }
            });

            // Category filter changes
            $(document).on('change', '.vlog-category-filter', function() {
                var url = $(this).val();
                if (url) {
                    window.location.href = url;
                }
            });

            // Keyboard navigation
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27) { // Escape key
                    self.closeLightbox();
                }
            });
        },

        // Initialize lightbox functionality
        initLightbox: function() {
            if (!this.config.lightboxEnabled) {
                return;
            }

            // Create lightbox HTML if it doesn't exist
            if ($('.vlog-lightbox').length === 0) {
                $('body').append(
                    '<div class="vlog-lightbox">' +
                        '<div class="vlog-lightbox-content">' +
                            '<button class="vlog-lightbox-close">&times;</button>' +
                            '<iframe src="" frameborder="0" allowfullscreen></iframe>' +
                        '</div>' +
                    '</div>'
                );
            }

            // Bind lightbox events
            var self = this;
            
            $('.vlog-lightbox-close').on('click', function() {
                self.closeLightbox();
            });

            $('.vlog-lightbox').on('click', function(e) {
                if (e.target === this) {
                    self.closeLightbox();
                }
            });
        },

        // Open lightbox with video
        openLightbox: function(embedUrl) {
            if (!embedUrl) return;

            // Add autoplay parameter
            var separator = embedUrl.includes('?') ? '&' : '?';
            var autoplayUrl = embedUrl + separator + 'autoplay=1';

            $('.vlog-lightbox iframe').attr('src', autoplayUrl);
            $('.vlog-lightbox').addClass('active');
            $('body').addClass('vlog-lightbox-open');

            // Prevent body scrolling
            $('body').css('overflow', 'hidden');
        },

        // Close lightbox
        closeLightbox: function() {
            $('.vlog-lightbox').removeClass('active');
            $('.vlog-lightbox iframe').attr('src', '');
            $('body').removeClass('vlog-lightbox-open');
            
            // Restore body scrolling
            $('body').css('overflow', '');
        },

        // Initialize load more functionality
        initLoadMore: function() {
            var self = this;

            $(document).on('click', '.vlog-load-more-btn', function(e) {
                e.preventDefault();
                
                var $button = $(this);
                var page = parseInt($button.data('page')) || 1;
                var category = $button.data('category') || '';
                var postsPerPage = parseInt($button.data('posts-per-page')) || 12;

                self.loadMoreVideos($button, page, category, postsPerPage);
            });
        },

        // Load more videos via AJAX
        loadMoreVideos: function($button, page, category, postsPerPage) {
            var self = this;
            var $gallery = $button.closest('.vlog-gallery').find('.vlog-gallery-grid');

            // Update button state
            $button.prop('disabled', true).text(this.config.strings.loading || 'Loading...');

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'load_more_videos',
                    page: page,
                    category: category,
                    posts_per_page: postsPerPage,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success && response.data.html) {
                        // Append new videos
                        $gallery.append(response.data.html);
                        
                        // Update button
                        if (response.data.has_more) {
                            $button.data('page', page + 1)
                                   .prop('disabled', false)
                                   .text(self.config.strings.loadMore || 'Load More');
                        } else {
                            $button.text(self.config.strings.noMoreVideos || 'No more videos')
                                   .prop('disabled', true);
                        }

                        // Initialize lazy loading for new items
                        self.initLazyLoading();
                        
                        // Trigger custom event
                        $(document).trigger('vlog:videosLoaded', [response.data.html]);
                    } else {
                        $button.text(self.config.strings.loadMore || 'Load More')
                               .prop('disabled', false);
                    }
                },
                error: function() {
                    $button.text(self.config.strings.loadMore || 'Load More')
                           .prop('disabled', false);
                }
            });
        },

        // Initialize filters
        initFilters: function() {
            // Smooth scrolling for filter changes
            $('.vlog-category-filter').on('change', function() {
                var $this = $(this);
                if ($this.val()) {
                    // Add loading state
                    $this.prop('disabled', true);
                    
                    // Re-enable after a short delay (page will likely redirect)
                    setTimeout(function() {
                        $this.prop('disabled', false);
                    }, 1000);
                }
            });
        },

        // Initialize lazy loading for images
        initLazyLoading: function() {
            if ('IntersectionObserver' in window) {
                var imageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            var img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img[data-src]').forEach(function(img) {
                    imageObserver.observe(img);
                });
            } else {
                // Fallback for older browsers
                $('img[data-src]').each(function() {
                    $(this).attr('src', $(this).data('src')).removeClass('lazy');
                });
            }
        },

        // Utility function to format duration
        formatDuration: function(seconds) {
            if (!seconds) return '';
            
            var hours = Math.floor(seconds / 3600);
            var minutes = Math.floor((seconds % 3600) / 60);
            var secs = seconds % 60;
            
            if (hours > 0) {
                return hours + ':' + (minutes < 10 ? '0' : '') + minutes + ':' + (secs < 10 ? '0' : '') + secs;
            } else {
                return minutes + ':' + (secs < 10 ? '0' : '') + secs;
            }
        },

        // Utility function to debounce function calls
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },

        // Analytics tracking (if needed)
        trackEvent: function(action, label, value) {
            // Google Analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', action, {
                    event_category: 'Vlog Plugin',
                    event_label: label,
                    value: value
                });
            }
            
            // Google Analytics Universal
            if (typeof ga !== 'undefined') {
                ga('send', 'event', 'Vlog Plugin', action, label, value);
            }
        },

        // Handle video play events
        onVideoPlay: function(videoId, platform) {
            this.trackEvent('video_play', platform + '_' + videoId);
            
            // Trigger custom event
            $(document).trigger('vlog:videoPlay', [videoId, platform]);
        },

        // Handle video end events
        onVideoEnd: function(videoId, platform) {
            this.trackEvent('video_complete', platform + '_' + videoId);
            
            // Trigger custom event
            $(document).trigger('vlog:videoEnd', [videoId, platform]);
        }
    };

    // Expose utility functions globally
    window.VlogUtils = {
        formatDuration: VlogPlugin.formatDuration,
        debounce: VlogPlugin.debounce
    };

    // Handle responsive video embeds
    $(window).on('resize', VlogPlugin.debounce(function() {
        $('.vlog-video-wrapper').each(function() {
            var $wrapper = $(this);
            var $iframe = $wrapper.find('iframe');
            
            if ($iframe.length) {
                var aspectRatio = $iframe.data('aspect-ratio') || (9/16);
                var width = $wrapper.width();
                var height = width * aspectRatio;
                
                $iframe.height(height);
            }
        });
    }, 250));

    // Handle print styles
    window.addEventListener('beforeprint', function() {
        $('.vlog-lightbox').removeClass('active');
    });

    // Handle page visibility changes (pause videos when tab is hidden)
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // Pause videos if possible
            $('.vlog-lightbox iframe').each(function() {
                var src = this.src;
                if (src) {
                    this.src = src.replace('autoplay=1', 'autoplay=0');
                }
            });
        }
    });

})(jQuery);
