<?php
/**
 * Rumble Video Parser
 *
 * @package Anthropora\VlogPlugin\API\Parsers
 */

namespace Anthropora\VlogPlugin\API\Parsers;

/**
 * Rumble video parser
 */
class RumbleParser implements ParserInterface {

    /**
     * Check if parser can handle the given URL
     *
     * @param string $url Video URL
     * @return bool
     */
    public function can_parse(string $url): bool {
        return (bool) preg_match('/rumble\.com/', $url);
    }

    /**
     * Parse video URL and extract metadata
     *
     * @param string $url Video URL
     * @return array|false Video data or false on failure
     */
    public function parse(string $url) {
        $video_id = $this->get_video_id($url);
        
        if (!$video_id) {
            return false;
        }

        $cache_key = "vlog_plugin_rumble_{$video_id}";
        $cached_data = get_transient($cache_key);
        
        if ($cached_data !== false) {
            return $cached_data;
        }

        // Parse using web scraping (Rumble doesn't have public API)
        $data = $this->scrape_video_data($url);
        
        if ($data) {
            $data['video_id'] = $video_id;
            $data['embed_url'] = $this->get_embed_url($video_id);
            
            // Cache for 1 hour
            set_transient($cache_key, $data, HOUR_IN_SECONDS);
        }

        return $data;
    }

    /**
     * Extract video ID from URL
     *
     * @param string $url Video URL
     * @return string|false Video ID or false on failure
     */
    public function get_video_id(string $url) {
        // Rumble URL patterns:
        // https://rumble.com/v123abc-video-title.html
        // https://rumble.com/embed/v123abc/
        
        $patterns = [
            '/rumble\.com\/v([a-zA-Z0-9]+)/',
            '/rumble\.com\/embed\/v([a-zA-Z0-9]+)/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return $matches[1];
            }
        }

        return false;
    }

    /**
     * Get embed URL for video
     *
     * @param string $video_id Video ID
     * @return string Embed URL
     */
    public function get_embed_url(string $video_id): string {
        return "https://rumble.com/embed/v{$video_id}/";
    }

    /**
     * Scrape video data from Rumble page
     *
     * @param string $url Video URL
     * @return array|false Video data or false on failure
     */
    private function scrape_video_data(string $url) {
        $response = wp_remote_get($url, [
            'timeout' => 30,
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            ],
        ]);

        if (is_wp_error($response)) {
            return false;
        }

        $html = wp_remote_retrieve_body($response);
        
        if (empty($html)) {
            return false;
        }

        // Extract title from meta tags or page title
        $title = $this->extract_title($html);
        $description = $this->extract_description($html);
        $thumbnail = $this->extract_thumbnail($html);
        $author = $this->extract_author($html);
        $duration = $this->extract_duration($html);

        if (empty($title)) {
            return false;
        }

        return [
            'title' => $title,
            'description' => $description,
            'thumbnail' => $thumbnail,
            'author' => $author,
            'duration' => $duration,
            'published_at' => '',
            'view_count' => 0,
        ];
    }

    /**
     * Extract title from HTML
     *
     * @param string $html HTML content
     * @return string Title
     */
    private function extract_title(string $html): string {
        // Try meta property first
        if (preg_match('/<meta property="og:title" content="([^"]+)"/i', $html, $matches)) {
            return html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8');
        }

        // Try title tag
        if (preg_match('/<title>([^<]+)</i', $html, $matches)) {
            $title = html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8');
            // Remove " - Rumble" suffix if present
            return preg_replace('/ - Rumble$/', '', $title);
        }

        return '';
    }

    /**
     * Extract description from HTML
     *
     * @param string $html HTML content
     * @return string Description
     */
    private function extract_description(string $html): string {
        if (preg_match('/<meta property="og:description" content="([^"]+)"/i', $html, $matches)) {
            return html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8');
        }

        if (preg_match('/<meta name="description" content="([^"]+)"/i', $html, $matches)) {
            return html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8');
        }

        return '';
    }

    /**
     * Extract thumbnail from HTML
     *
     * @param string $html HTML content
     * @return string Thumbnail URL
     */
    private function extract_thumbnail(string $html): string {
        if (preg_match('/<meta property="og:image" content="([^"]+)"/i', $html, $matches)) {
            return $matches[1];
        }

        return '';
    }

    /**
     * Extract author from HTML
     *
     * @param string $html HTML content
     * @return string Author name
     */
    private function extract_author(string $html): string {
        // Try to find author in various places
        if (preg_match('/<meta name="author" content="([^"]+)"/i', $html, $matches)) {
            return html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8');
        }

        // Look for channel name patterns
        if (preg_match('/channel\/([^"\']+)/i', $html, $matches)) {
            return html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8');
        }

        return '';
    }

    /**
     * Extract duration from HTML
     *
     * @param string $html HTML content
     * @return int Duration in seconds
     */
    private function extract_duration(string $html): int {
        // Look for duration in meta tags or structured data
        if (preg_match('/"duration":"PT(\d+)M(\d+)S"/i', $html, $matches)) {
            return (int)$matches[1] * 60 + (int)$matches[2];
        }

        if (preg_match('/"duration":"(\d+)"/i', $html, $matches)) {
            return (int)$matches[1];
        }

        return 0;
    }
}
