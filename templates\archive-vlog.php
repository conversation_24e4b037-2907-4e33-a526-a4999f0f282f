<?php
/**
 * Archive Vlog Template
 *
 * @package Anthropora\VlogPlugin
 */

get_header(); ?>

<div class="vlog-archive-container">
    <header class="vlog-archive-header">
        <h1 class="vlog-archive-title">
            <?php
            if (is_post_type_archive('vlog')) {
                esc_html_e('Video Blog', 'vlog-plugin');
            } else {
                the_archive_title();
            }
            ?>
        </h1>
        
        <?php if (is_post_type_archive('vlog')): ?>
            <p class="vlog-archive-description">
                <?php esc_html_e('Browse our collection of videos', 'vlog-plugin'); ?>
            </p>
        <?php else: ?>
            <?php the_archive_description('<div class="vlog-archive-description">', '</div>'); ?>
        <?php endif; ?>
    </header>

    <div class="vlog-archive-content">
        <?php
        // Display category filter
        $categories = get_terms([
            'taxonomy' => 'vlog_category',
            'hide_empty' => true,
        ]);
        
        if (!empty($categories) && !is_wp_error($categories)):
        ?>
            <div class="vlog-archive-filters">
                <div class="vlog-filter-categories">
                    <label for="vlog-category-filter"><?php esc_html_e('Filter by Category:', 'vlog-plugin'); ?></label>
                    <select id="vlog-category-filter" class="vlog-category-filter">
                        <option value=""><?php esc_html_e('All Categories', 'vlog-plugin'); ?></option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo esc_attr(get_term_link($category)); ?>">
                                <?php echo esc_html($category->name); ?> (<?php echo esc_html($category->count); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
        <?php endif; ?>

        <?php if (have_posts()): ?>
            <div class="vlog-archive-grid vlog-columns-3">
                <?php while (have_posts()): the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class('vlog-archive-item'); ?>>
                        <?php
                        $thumbnail = get_post_meta(get_the_ID(), '_vlog_custom_thumbnail', true);
                        if (!$thumbnail) {
                            $thumbnail = get_post_meta(get_the_ID(), '_vlog_thumbnail_url', true);
                        }
                        
                        $duration = get_post_meta(get_the_ID(), '_vlog_duration', true);
                        $platform = get_post_meta(get_the_ID(), '_vlog_platform', true);
                        $author = get_post_meta(get_the_ID(), '_vlog_author', true);
                        $published_at = get_post_meta(get_the_ID(), '_vlog_published_at', true);
                        $embed_url = get_post_meta(get_the_ID(), '_vlog_embed_url', true);
                        $is_featured = get_post_meta(get_the_ID(), '_vlog_featured_video', true);
                        
                        $options = get_option('vlog_plugin_options', []);
                        ?>
                        
                        <div class="vlog-item-thumbnail <?php echo $is_featured ? 'vlog-featured' : ''; ?>">
                            <a href="<?php the_permalink(); ?>">
                                <?php if ($thumbnail): ?>
                                    <img src="<?php echo esc_url($thumbnail); ?>" 
                                         alt="<?php echo esc_attr(get_the_title()); ?>"
                                         loading="lazy" />
                                <?php endif; ?>
                                
                                <?php if ($duration && ($options['show_video_duration'] ?? true)): ?>
                                    <span class="vlog-duration"><?php echo esc_html($this->format_duration($duration)); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($platform): ?>
                                    <span class="vlog-platform vlog-platform-<?php echo esc_attr($platform); ?>">
                                        <?php echo esc_html(ucfirst($platform)); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($is_featured): ?>
                                    <span class="vlog-featured-badge"><?php esc_html_e('Featured', 'vlog-plugin'); ?></span>
                                <?php endif; ?>
                                
                                <div class="vlog-play-overlay">
                                    <span class="vlog-play-button">▶</span>
                                </div>
                            </a>
                        </div>
                        
                        <div class="vlog-item-content">
                            <h2 class="vlog-item-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h2>
                            
                            <?php if (has_excerpt()): ?>
                                <div class="vlog-item-excerpt">
                                    <?php the_excerpt(); ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="vlog-item-meta">
                                <?php if ($author): ?>
                                    <span class="vlog-author">
                                        <strong><?php esc_html_e('By:', 'vlog-plugin'); ?></strong> <?php echo esc_html($author); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($published_at && ($options['show_video_date'] ?? true)): ?>
                                    <span class="vlog-date">
                                        <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($published_at))); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <span class="vlog-post-date">
                                    <?php echo esc_html(get_the_date()); ?>
                                </span>
                            </div>
                            
                            <?php
                            // Display categories
                            $categories = get_the_terms(get_the_ID(), 'vlog_category');
                            if ($categories && !is_wp_error($categories)):
                            ?>
                                <div class="vlog-item-categories">
                                    <?php foreach ($categories as $category): ?>
                                        <a href="<?php echo esc_url(get_term_link($category)); ?>" class="vlog-category-tag">
                                            <?php echo esc_html($category->name); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>

            <?php
            // Pagination
            $pagination = paginate_links([
                'prev_text' => __('&laquo; Previous', 'vlog-plugin'),
                'next_text' => __('Next &raquo;', 'vlog-plugin'),
                'type' => 'array',
            ]);
            
            if ($pagination):
            ?>
                <nav class="vlog-pagination" role="navigation">
                    <h2 class="screen-reader-text"><?php esc_html_e('Videos navigation', 'vlog-plugin'); ?></h2>
                    <ul class="vlog-pagination-list">
                        <?php foreach ($pagination as $page): ?>
                            <li><?php echo $page; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </nav>
            <?php endif; ?>

        <?php else: ?>
            <div class="vlog-no-videos">
                <h2><?php esc_html_e('No videos found', 'vlog-plugin'); ?></h2>
                <p><?php esc_html_e('Sorry, no videos were found matching your criteria.', 'vlog-plugin'); ?></p>
                
                <?php if (current_user_can('edit_posts')): ?>
                    <p>
                        <a href="<?php echo admin_url('post-new.php?post_type=vlog'); ?>" class="button">
                            <?php esc_html_e('Add Your First Video', 'vlog-plugin'); ?>
                        </a>
                    </p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Category filter functionality
    const categoryFilter = document.getElementById('vlog-category-filter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            if (this.value) {
                window.location.href = this.value;
            } else {
                window.location.href = '<?php echo esc_js(get_post_type_archive_link('vlog')); ?>';
            }
        });
    }
});
</script>

<?php
/**
 * Format duration helper function
 */
function format_duration($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;

    if ($hours > 0) {
        return sprintf('%d:%02d:%02d', $hours, $minutes, $secs);
    } else {
        return sprintf('%d:%02d', $minutes, $secs);
    }
}
?>

<?php get_footer(); ?>
