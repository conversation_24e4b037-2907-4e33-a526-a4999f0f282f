<?php
/**
 * Plugin Deactivator
 *
 * @package Anthropora\VlogPlugin
 */

namespace Anthropora\VlogPlugin;

/**
 * Handles plugin deactivation
 */
class Deactivator {

    /**
     * Deactivate the plugin
     */
    public static function deactivate(): void {
        // Clear scheduled cron events
        wp_clear_scheduled_hook('vlog_plugin_cleanup_cache');

        // Flush rewrite rules
        flush_rewrite_rules();

        // Clear any cached data
        self::clear_cache();
    }

    /**
     * Clear plugin cache
     */
    private static function clear_cache(): void {
        // Clear WordPress transients
        global $wpdb;
        
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_vlog_plugin_%'
            )
        );

        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_timeout_vlog_plugin_%'
            )
        );
    }
}
