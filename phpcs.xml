<?xml version="1.0"?>
<ruleset name="Vlog Plugin Coding Standards">
    <description>WordPress Coding Standards for Vlog Plugin</description>

    <!-- Files to check -->
    <file>src</file>
    <file>vlog-plugin.php</file>

    <!-- Exclude patterns -->
    <exclude-pattern>*/vendor/*</exclude-pattern>
    <exclude-pattern>*/node_modules/*</exclude-pattern>
    <exclude-pattern>*/tests/*</exclude-pattern>

    <!-- WordPress Coding Standards -->
    <rule ref="WordPress">
        <!-- Allow short array syntax -->
        <exclude name="Generic.Arrays.DisallowShortArraySyntax"/>
        <!-- Allow short open tags in templates -->
        <exclude name="Generic.PHP.DisallowShortOpenTag.EchoFound"/>
    </rule>

    <!-- WordPress Extra Standards -->
    <rule ref="WordPress-Extra"/>

    <!-- WordPress Security Standards -->
    <rule ref="WordPress.Security"/>

    <!-- WordPress Documentation Standards -->
    <rule ref="WordPress-Docs"/>

    <!-- Check for PHP cross-version compatibility -->
    <rule ref="PHPCompatibilityWP"/>

    <!-- Set minimum PHP version -->
    <config name="testVersion" value="7.4-"/>

    <!-- Set text domain -->
    <rule ref="WordPress.WP.I18n">
        <properties>
            <property name="text_domain" type="array" value="vlog-plugin"/>
        </properties>
    </rule>

    <!-- Verify that everything in the global namespace is prefixed -->
    <rule ref="WordPress.NamingConventions.PrefixAllGlobals">
        <properties>
            <property name="prefixes" type="array" value="vlog_plugin,VLOG_PLUGIN"/>
        </properties>
    </rule>
</ruleset>
