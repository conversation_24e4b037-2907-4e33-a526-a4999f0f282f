{"name": "vlog-plugin", "version": "1.0.0", "description": "Professional WordPress Vlog Plugin for Video Blog Management", "main": "assets/js/frontend.js", "scripts": {"build": "npm run build:css && npm run build:js", "build:css": "sass assets/scss:assets/css --style compressed", "build:js": "webpack --mode production", "dev": "npm run dev:css && npm run dev:js", "dev:css": "sass assets/scss:assets/css --watch", "dev:js": "webpack --mode development --watch", "lint": "npm run lint:css && npm run lint:js", "lint:css": "stylelint 'assets/scss/**/*.scss'", "lint:js": "eslint 'assets/js/**/*.js'", "test": "jest", "test:watch": "jest --watch", "zip": "npm run build && zip -r vlog-plugin.zip . -x 'node_modules/*' '.git/*' 'tests/*' '*.log' '.env*'", "release": "npm run build && npm run zip"}, "repository": {"type": "git", "url": "https://github.com/anthropora/vlog-plugin.git"}, "keywords": ["wordpress", "plugin", "video", "vlog", "youtube", "rumble", "gallery"], "author": "Anthropora <<EMAIL>>", "license": "GPL-2.0-or-later", "bugs": {"url": "https://github.com/anthropora/vlog-plugin/issues"}, "homepage": "https://anthropora.com/vlog-plugin", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "eslint": "^8.42.0", "eslint-config-wordpress": "^2.0.0", "jest": "^29.5.0", "mini-css-extract-plugin": "^2.7.0", "sass": "^1.63.0", "sass-loader": "^13.3.0", "stylelint": "^15.7.0", "stylelint-config-wordpress": "^17.0.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "dependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/js/setup.js"], "testMatch": ["<rootDir>/tests/js/**/*.test.js"], "collectCoverageFrom": ["assets/js/**/*.js", "!assets/js/**/*.min.js"]}, "eslintConfig": {"extends": ["wordpress"], "env": {"browser": true, "jquery": true, "es6": true}, "globals": {"wp": "readonly", "vlogFrontend": "readonly", "VlogPlugin": "writable"}}, "stylelint": {"extends": ["stylelint-config-wordpress"], "rules": {"no-descending-specificity": null, "declaration-property-unit-allowed-list": null}}}