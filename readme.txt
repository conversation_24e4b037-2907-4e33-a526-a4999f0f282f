=== Professional Vlog Plugin ===
Contributors: anthropora
Tags: video, vlog, youtube, rumble, gallery, video-blog
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

A comprehensive WordPress plugin for managing video blogs with YouTube and Rumble integration, featuring video galleries, categories, and pagination.

== Description ==

Transform your WordPress site into a professional video blog platform with the Professional Vlog Plugin. This feature-rich plugin provides everything you need to create, manage, and display video content from YouTube and Rumble platforms.

= Key Features =

**🎥 Multi-Platform Video Support**
* YouTube integration with API support
* Rumble video parsing and display
* Automatic metadata extraction (titles, descriptions, thumbnails)
* Video duration and view count display

**🎨 Beautiful Frontend Display**
* Responsive video galleries with grid layouts
* Lightbox modal video playback
* Category-based filtering system
* Pagination and load-more functionality
* Mobile-optimized design

**🔧 Powerful Admin Interface**
* WordPress-native admin experience
* One-click video URL parsing
* Bulk video import functionality
* Featured video management
* Comprehensive settings panel

**🚀 Performance & Security**
* Multi-layer caching system
* Security hardened with input sanitization
* Rate limiting and nonce verification
* SEO optimized with structured data
* Clean, semantic HTML output

= Perfect For =

* Content creators and influencers
* Educational institutions
* Corporate video libraries
* Entertainment websites
* Tutorial and training sites
* Video portfolios

= Shortcodes =

Display video galleries anywhere with powerful shortcodes:

`[vlog_gallery posts_per_page="12" columns="3" category="tech"]`
`[vlog_video id="123"]`
`[vlog_categories style="list"]`
`[vlog_featured limit="6"]`

= Developer Friendly =

* PSR-4 autoloading
* Comprehensive hook system
* Template override support
* REST API integration
* Extensive documentation

== Installation ==

= Automatic Installation =

1. Log in to your WordPress admin panel
2. Navigate to Plugins > Add New
3. Search for "Professional Vlog Plugin"
4. Click "Install Now" and then "Activate"

= Manual Installation =

1. Download the plugin zip file
2. Upload to `/wp-content/plugins/` directory
3. Extract the files
4. Activate the plugin through the WordPress admin panel

= After Installation =

1. Go to Vlogs > Settings to configure the plugin
2. Add your first video by going to Vlogs > Add New
3. Use shortcodes or visit the auto-created Video Blog page

== Frequently Asked Questions ==

= What video platforms are supported? =

Currently, the plugin supports YouTube and Rumble. We're working on adding support for additional platforms in future releases.

= Do I need API keys to use the plugin? =

API keys are optional but recommended. For YouTube, you can add a YouTube Data API v3 key for enhanced metadata extraction. The plugin will work without API keys using fallback methods.

= Can I customize the video gallery appearance? =

Yes! The plugin includes customizable CSS and supports template overrides. You can copy template files to your theme directory for complete customization.

= Is the plugin mobile-friendly? =

Absolutely! The plugin is fully responsive and optimized for all device sizes, including tablets and smartphones.

= Can I import multiple videos at once? =

Yes, the plugin includes a bulk import feature. Go to Vlogs > Import and paste multiple video URLs (one per line) to import them all at once.

= Does the plugin affect site performance? =

The plugin is optimized for performance with built-in caching, lazy loading, and efficient database queries. It should not negatively impact your site's speed.

= Can I organize videos into categories? =

Yes, the plugin includes a hierarchical category system similar to WordPress posts. You can create categories and subcategories to organize your videos.

= Is the plugin translation-ready? =

Yes, the plugin is fully internationalized and ready for translation. Translation files are included for easy localization.

== Screenshots ==

1. Video gallery with responsive grid layout
2. Admin interface for adding and managing videos
3. Single video page with player and metadata
4. Bulk import interface for adding multiple videos
5. Plugin settings panel with configuration options
6. Category management interface
7. Lightbox video player in action
8. Mobile-responsive video gallery

== Changelog ==

= 1.0.0 =
* Initial release
* YouTube and Rumble integration
* Responsive video gallery system
* Admin interface with bulk operations
* Caching and performance optimizations
* Security hardening and input validation
* SEO optimization with structured data
* Comprehensive shortcode system
* Template override support
* Multi-language support
* Extensive documentation

== Upgrade Notice ==

= 1.0.0 =
Initial release of the Professional Vlog Plugin. Install now to start creating your video blog!

== Support ==

For support, documentation, and feature requests, please visit:

* [Plugin Documentation](https://anthropora.com/vlog-plugin/docs)
* [Support Forum](https://wordpress.org/support/plugin/vlog-plugin)
* [GitHub Repository](https://github.com/anthropora/vlog-plugin)

== Privacy Policy ==

This plugin does not collect or store any personal data from your website visitors. Video metadata is cached locally on your server for performance optimization. When using YouTube API integration, requests are made directly to Google's servers following their privacy policies.

== Credits ==

Developed by [Anthropora](https://anthropora.com) - Professional WordPress Development

Special thanks to the WordPress community and all contributors who helped make this plugin possible.
