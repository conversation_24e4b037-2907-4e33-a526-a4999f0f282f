/**
 * Frontend Styles for Vlog Plugin
 */

/* Video Gallery Styles */
.vlog-gallery {
    margin: 20px 0;
}

.vlog-gallery-grid {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

.vlog-columns-1 { grid-template-columns: 1fr; }
.vlog-columns-2 { grid-template-columns: repeat(2, 1fr); }
.vlog-columns-3 { grid-template-columns: repeat(3, 1fr); }
.vlog-columns-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .vlog-columns-2,
    .vlog-columns-3,
    .vlog-columns-4 {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .vlog-columns-3,
    .vlog-columns-4 {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Gallery Item Styles */
.vlog-gallery-item,
.vlog-archive-item {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vlog-gallery-item:hover,
.vlog-archive-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.vlog-gallery-item.vlog-featured {
    border: 2px solid #007cba;
}

/* Thumbnail Styles */
.vlog-item-thumbnail {
    position: relative;
    overflow: hidden;
    aspect-ratio: 16/9;
}

.vlog-item-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.vlog-item-thumbnail:hover img {
    transform: scale(1.05);
}

.vlog-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.vlog-platform {
    position: absolute;
    top: 8px;
    left: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.vlog-platform-youtube {
    background: #ff0000;
    color: white;
}

.vlog-platform-rumble {
    background: #85c742;
    color: white;
}

.vlog-featured-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #007cba;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
}

.vlog-play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.vlog-item-thumbnail:hover .vlog-play-overlay {
    opacity: 1;
}

.vlog-play-button {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #333;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.vlog-play-button:hover {
    transform: scale(1.1);
}

/* Content Styles */
.vlog-item-content {
    padding: 16px;
}

.vlog-item-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    line-height: 1.4;
}

.vlog-item-title a {
    color: #333;
    text-decoration: none;
}

.vlog-item-title a:hover {
    color: #007cba;
}

.vlog-item-excerpt {
    margin: 8px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.vlog-item-meta {
    margin-top: 12px;
    font-size: 12px;
    color: #888;
}

.vlog-item-meta span {
    margin-right: 12px;
}

.vlog-item-categories {
    margin-top: 8px;
}

.vlog-category-tag {
    display: inline-block;
    background: #f0f0f0;
    color: #666;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    text-decoration: none;
    margin-right: 4px;
    margin-bottom: 4px;
}

.vlog-category-tag:hover {
    background: #007cba;
    color: white;
}

/* Single Video Styles */
.vlog-video-player {
    margin: 20px 0;
}

.vlog-video-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
    border-radius: 8px;
}

.vlog-video-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.vlog-single-meta {
    margin: 16px 0;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 8px;
}

.vlog-single-meta span {
    display: inline-block;
    margin-right: 20px;
    margin-bottom: 8px;
}

.vlog-categories,
.vlog-tags {
    margin: 12px 0;
}

.vlog-category-link,
.vlog-tag-link {
    display: inline-block;
    background: #007cba;
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    text-decoration: none;
    font-size: 12px;
    margin-right: 8px;
    margin-bottom: 4px;
}

.vlog-tag-link {
    background: #666;
}

.vlog-category-link:hover,
.vlog-tag-link:hover {
    opacity: 0.8;
}

/* Navigation Styles */
.vlog-navigation {
    margin: 40px 0;
    padding: 20px 0;
    border-top: 1px solid #eee;
}

.vlog-nav-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.vlog-nav-previous,
.vlog-nav-next {
    padding: 16px;
    background: #f9f9f9;
    border-radius: 8px;
}

.vlog-nav-next {
    text-align: right;
}

.vlog-nav-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.vlog-nav-title {
    display: block;
    font-weight: bold;
    color: #333;
}

/* Related Videos */
.vlog-related-videos {
    margin: 40px 0;
}

.vlog-related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.vlog-related-item {
    background: #f9f9f9;
    border-radius: 8px;
    overflow: hidden;
}

.vlog-related-link {
    display: block;
    text-decoration: none;
    color: #333;
}

.vlog-related-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.vlog-related-item h4 {
    padding: 12px;
    margin: 0;
    font-size: 14px;
    line-height: 1.3;
}

/* Filters */
.vlog-filters,
.vlog-archive-filters {
    margin: 20px 0;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 8px;
}

.vlog-filter-categories label {
    display: inline-block;
    margin-right: 12px;
    font-weight: bold;
}

.vlog-category-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

/* Pagination */
.vlog-pagination {
    margin: 40px 0;
    text-align: center;
}

.vlog-pagination-list {
    display: inline-flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 4px;
}

.vlog-pagination-list li {
    margin: 0;
}

.vlog-pagination a,
.vlog-pagination .current {
    display: block;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
}

.vlog-pagination a:hover,
.vlog-pagination .current {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

/* Load More */
.vlog-load-more {
    text-align: center;
    margin: 30px 0;
}

.vlog-load-more-btn {
    background: #007cba;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

.vlog-load-more-btn:hover {
    background: #005a87;
}

.vlog-load-more-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* No Videos */
.vlog-no-videos {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

/* Breadcrumbs */
.vlog-breadcrumbs {
    margin: 16px 0;
    font-size: 14px;
    color: #666;
}

.vlog-breadcrumbs a {
    color: #007cba;
    text-decoration: none;
}

.vlog-breadcrumbs a:hover {
    text-decoration: underline;
}

.vlog-breadcrumbs .current {
    color: #333;
    font-weight: bold;
}

/* Lightbox Styles */
.vlog-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
}

.vlog-lightbox.active {
    display: flex;
}

.vlog-lightbox-content {
    position: relative;
    width: 90%;
    max-width: 1200px;
    aspect-ratio: 16/9;
}

.vlog-lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 8px;
}

.vlog-lightbox iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 480px) {
    .vlog-item-content {
        padding: 12px;
    }
    
    .vlog-item-title {
        font-size: 14px;
    }
    
    .vlog-nav-links {
        grid-template-columns: 1fr;
    }
    
    .vlog-related-grid {
        grid-template-columns: 1fr;
    }
    
    .vlog-lightbox-content {
        width: 95%;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .vlog-gallery-item,
    .vlog-archive-item {
        background: #2a2a2a;
        color: #fff;
    }
    
    .vlog-item-title a {
        color: #fff;
    }
    
    .vlog-single-meta {
        background: #2a2a2a;
    }
    
    .vlog-filters,
    .vlog-archive-filters {
        background: #2a2a2a;
    }
    
    .vlog-category-filter {
        background: #2a2a2a;
        color: #fff;
        border-color: #555;
    }
}
