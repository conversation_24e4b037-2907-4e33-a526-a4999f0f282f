<?php
/**
 * Security Manager
 *
 * @package Anthropora\VlogPlugin\Core
 */

namespace Anthropora\VlogPlugin\Core;

/**
 * Handles security measures and validation
 */
class Security {

    /**
     * Initialize security measures
     */
    public function init(): void {
        add_action('init', [$this, 'setup_security_headers']);
        add_filter('wp_kses_allowed_html', [$this, 'allow_iframe_in_content'], 10, 2);
        add_action('wp_ajax_vlog_security_check', [$this, 'ajax_security_check']);
        add_action('wp_ajax_nopriv_vlog_security_check', [$this, 'ajax_security_check']);
    }

    /**
     * Setup security headers
     */
    public function setup_security_headers(): void {
        if (!headers_sent()) {
            // Content Security Policy for video embeds
            $csp_sources = apply_filters('vlog_plugin_csp_sources', [
                'youtube.com',
                'www.youtube.com',
                'youtube-nocookie.com',
                'www.youtube-nocookie.com',
                'rumble.com',
                'www.rumble.com',
            ]);

            if (!empty($csp_sources)) {
                $csp = "frame-src 'self' " . implode(' ', array_map('esc_attr', $csp_sources)) . ";";
                header("Content-Security-Policy: " . $csp);
            }
        }
    }

    /**
     * Allow iframe in content for video embeds
     *
     * @param array  $allowed_html Allowed HTML tags
     * @param string $context      Context
     * @return array Modified allowed HTML
     */
    public function allow_iframe_in_content($allowed_html, $context): array {
        if ($context === 'post') {
            $allowed_html['iframe'] = [
                'src' => true,
                'width' => true,
                'height' => true,
                'frameborder' => true,
                'allowfullscreen' => true,
                'loading' => true,
                'title' => true,
                'class' => true,
                'id' => true,
            ];
        }

        return $allowed_html;
    }

    /**
     * AJAX security check
     */
    public function ajax_security_check(): void {
        check_ajax_referer('vlog_security_nonce', 'nonce');
        wp_send_json_success(['status' => 'verified']);
    }

    /**
     * Validate video URL
     *
     * @param string $url Video URL
     * @return bool|string Valid URL or false
     */
    public static function validate_video_url($url): bool|string {
        // Basic URL validation
        $url = trim($url);
        
        if (empty($url)) {
            return false;
        }

        // Validate URL format
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        // Parse URL
        $parsed = parse_url($url);
        
        if (!$parsed || !isset($parsed['host'])) {
            return false;
        }

        // Check allowed domains
        $allowed_domains = apply_filters('vlog_plugin_allowed_domains', [
            'youtube.com',
            'www.youtube.com',
            'youtu.be',
            'rumble.com',
            'www.rumble.com',
        ]);

        $host = strtolower($parsed['host']);
        
        if (!in_array($host, $allowed_domains)) {
            return false;
        }

        // Additional security checks
        if (strpos($url, 'javascript:') !== false || strpos($url, 'data:') !== false) {
            return false;
        }

        return esc_url_raw($url);
    }

    /**
     * Sanitize video data
     *
     * @param array $data Raw video data
     * @return array Sanitized data
     */
    public static function sanitize_video_data($data): array {
        if (!is_array($data)) {
            return [];
        }

        $sanitized = [];

        // Sanitize each field
        $sanitized['title'] = sanitize_text_field($data['title'] ?? '');
        $sanitized['description'] = wp_kses_post($data['description'] ?? '');
        $sanitized['thumbnail'] = esc_url_raw($data['thumbnail'] ?? '');
        $sanitized['duration'] = absint($data['duration'] ?? 0);
        $sanitized['video_id'] = sanitize_text_field($data['video_id'] ?? '');
        $sanitized['embed_url'] = self::validate_video_url($data['embed_url'] ?? '');
        $sanitized['platform'] = sanitize_key($data['platform'] ?? '');
        $sanitized['original_url'] = self::validate_video_url($data['original_url'] ?? '');
        $sanitized['author'] = sanitize_text_field($data['author'] ?? '');
        $sanitized['published_at'] = sanitize_text_field($data['published_at'] ?? '');
        $sanitized['view_count'] = absint($data['view_count'] ?? 0);

        // Remove empty values
        return array_filter($sanitized, function($value) {
            return $value !== '' && $value !== false;
        });
    }

    /**
     * Validate user permissions for vlog operations
     *
     * @param string $action Action to check
     * @param int    $post_id Post ID (optional)
     * @return bool Permission status
     */
    public static function check_vlog_permissions($action, $post_id = 0): bool {
        switch ($action) {
            case 'create':
                return current_user_can('edit_posts');
                
            case 'edit':
                if ($post_id) {
                    return current_user_can('edit_post', $post_id);
                }
                return current_user_can('edit_posts');
                
            case 'delete':
                if ($post_id) {
                    return current_user_can('delete_post', $post_id);
                }
                return current_user_can('delete_posts');
                
            case 'manage_settings':
                return current_user_can('manage_options');
                
            case 'import':
                return current_user_can('edit_posts');
                
            default:
                return false;
        }
    }

    /**
     * Generate secure nonce for vlog operations
     *
     * @param string $action Action name
     * @return string Nonce
     */
    public static function create_nonce($action): string {
        return wp_create_nonce('vlog_plugin_' . $action);
    }

    /**
     * Verify nonce for vlog operations
     *
     * @param string $nonce  Nonce to verify
     * @param string $action Action name
     * @return bool Verification status
     */
    public static function verify_nonce($nonce, $action): bool {
        return wp_verify_nonce($nonce, 'vlog_plugin_' . $action);
    }

    /**
     * Rate limit API requests
     *
     * @param string $key Rate limit key
     * @param int    $limit Requests per minute
     * @return bool Whether request is allowed
     */
    public static function rate_limit($key, $limit = 60): bool {
        $transient_key = 'vlog_rate_limit_' . md5($key);
        $requests = get_transient($transient_key);

        if ($requests === false) {
            set_transient($transient_key, 1, MINUTE_IN_SECONDS);
            return true;
        }

        if ($requests >= $limit) {
            return false;
        }

        set_transient($transient_key, $requests + 1, MINUTE_IN_SECONDS);
        return true;
    }

    /**
     * Log security events
     *
     * @param string $event Event description
     * @param array  $data  Additional data
     */
    public static function log_security_event($event, $data = []): void {
        if (!WP_DEBUG_LOG) {
            return;
        }

        $log_data = [
            'timestamp' => current_time('mysql'),
            'event' => $event,
            'user_id' => get_current_user_id(),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'data' => $data,
        ];

        error_log('VLOG_PLUGIN_SECURITY: ' . wp_json_encode($log_data));
    }

    /**
     * Validate and sanitize import data
     *
     * @param array $urls Array of URLs to import
     * @return array Validated URLs
     */
    public static function validate_import_data($urls): array {
        if (!is_array($urls)) {
            return [];
        }

        $validated = [];
        $max_urls = apply_filters('vlog_plugin_max_import_urls', 50);

        foreach (array_slice($urls, 0, $max_urls) as $url) {
            $clean_url = self::validate_video_url($url);
            if ($clean_url) {
                $validated[] = $clean_url;
            }
        }

        return array_unique($validated);
    }

    /**
     * Escape output for different contexts
     *
     * @param mixed  $data    Data to escape
     * @param string $context Context (html, attr, url, js)
     * @return mixed Escaped data
     */
    public static function escape_output($data, $context = 'html') {
        switch ($context) {
            case 'attr':
                return esc_attr($data);
                
            case 'url':
                return esc_url($data);
                
            case 'js':
                return esc_js($data);
                
            case 'html':
            default:
                return esc_html($data);
        }
    }

    /**
     * Check if current request is from allowed referrer
     *
     * @return bool Referrer check status
     */
    public static function check_referrer(): bool {
        $referrer = wp_get_referer();
        
        if (!$referrer) {
            return false;
        }

        $site_url = site_url();
        return strpos($referrer, $site_url) === 0;
    }

    /**
     * Validate file upload for thumbnails
     *
     * @param array $file File data
     * @return bool|string Valid file path or false
     */
    public static function validate_thumbnail_upload($file): bool|string {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return false;
        }

        // Check file type
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $file_type = wp_check_filetype($file['name']);
        
        if (!in_array($file_type['type'], $allowed_types)) {
            return false;
        }

        // Check file size (max 5MB)
        $max_size = apply_filters('vlog_plugin_max_thumbnail_size', 5 * 1024 * 1024);
        
        if ($file['size'] > $max_size) {
            return false;
        }

        // Additional security checks
        $image_info = getimagesize($file['tmp_name']);
        
        if (!$image_info) {
            return false;
        }

        return $file['tmp_name'];
    }
}
