<?php
/**
 * Video Parser Interface
 *
 * @package Anthropora\VlogPlugin\API\Parsers
 */

namespace Anthropora\VlogPlugin\API\Parsers;

/**
 * Interface for video parsers
 */
interface ParserInterface {

    /**
     * Check if parser can handle the given URL
     *
     * @param string $url Video URL
     * @return bool
     */
    public function can_parse(string $url): bool;

    /**
     * Parse video URL and extract metadata
     *
     * @param string $url Video URL
     * @return array|false Video data or false on failure
     */
    public function parse(string $url);

    /**
     * Extract video ID from URL
     *
     * @param string $url Video URL
     * @return string|false Video ID or false on failure
     */
    public function get_video_id(string $url);

    /**
     * Get embed URL for video
     *
     * @param string $video_id Video ID
     * @return string Embed URL
     */
    public function get_embed_url(string $video_id): string;
}
