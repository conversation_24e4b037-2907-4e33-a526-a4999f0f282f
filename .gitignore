# WordPress
wp-config.php
wp-content/uploads/
wp-content/blogs.dir/
wp-content/upgrade/
wp-content/backup-db/
wp-content/advanced-cache.php
wp-content/wp-cache-config.php
wp-content/cache/
wp-content/cache/supercache/

# WordPress multisite
wp-content/blogs.dir/
wp-content/blog-deleted/
wp-content/blog-inactive/
wp-content/blog-suspended/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
error_log
access_log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.production

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage
.grunt

# Compiled binary addons
build/Release

# Users Environment Variables
.lock-wscript

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Composer
vendor/
composer.lock

# PHPUnit
phpunit.xml
.phpunit.result.cache

# PHP CS Fixer
.php_cs.cache
.php-cs-fixer.cache

# PHPStan
phpstan.neon.dist

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Build artifacts
assets/css/*.map
assets/js/*.map
assets/js/dist/
assets/css/dist/

# Plugin specific
languages/*.mo
languages/*.pot

# Test files
tests/tmp/
tests/_output/
tests/_support/_generated

# Local development
.htaccess.local
wp-config-local.php
local-config.php

# Security
.env.example
secrets.json
*.pem
*.key

# Documentation build
docs/_build/
docs/.doctrees/

# Webpack
webpack-stats.json

# Sass
.sass-cache/
*.css.map
*.sass.map
*.scss.map
