<?php
/**
 * Database Manager
 *
 * @package Anthropora\VlogPlugin\Database
 */

namespace Anthropora\VlogPlugin\Database;

/**
 * Handles database operations, post types, and taxonomies
 */
class DatabaseManager {

    /**
     * Register custom post types
     */
    public function register_post_types(): void {
        $this->register_vlog_post_type();
    }

    /**
     * Register custom taxonomies
     */
    public function register_taxonomies(): void {
        $this->register_vlog_category_taxonomy();
        $this->register_vlog_tag_taxonomy();
    }

    /**
     * Create custom database tables
     */
    public function create_tables(): void {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Video cache table
        $table_name = $wpdb->prefix . 'vlog_video_cache';
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            video_id varchar(255) NOT NULL,
            platform varchar(50) NOT NULL,
            title text NOT NULL,
            description longtext,
            thumbnail_url varchar(500),
            duration int(11) DEFAULT 0,
            author varchar(255),
            published_at datetime,
            view_count bigint(20) DEFAULT 0,
            embed_url varchar(500),
            original_url varchar(500),
            cached_at datetime DEFAULT CURRENT_TIMESTAMP,
            expires_at datetime,
            PRIMARY KEY (id),
            UNIQUE KEY video_platform (video_id, platform),
            KEY platform (platform),
            KEY expires_at (expires_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Register vlog post type
     */
    private function register_vlog_post_type(): void {
        $labels = [
            'name'                  => _x('Vlogs', 'Post type general name', 'vlog-plugin'),
            'singular_name'         => _x('Vlog', 'Post type singular name', 'vlog-plugin'),
            'menu_name'             => _x('Vlogs', 'Admin Menu text', 'vlog-plugin'),
            'name_admin_bar'        => _x('Vlog', 'Add New on Toolbar', 'vlog-plugin'),
            'add_new'               => __('Add New', 'vlog-plugin'),
            'add_new_item'          => __('Add New Vlog', 'vlog-plugin'),
            'new_item'              => __('New Vlog', 'vlog-plugin'),
            'edit_item'             => __('Edit Vlog', 'vlog-plugin'),
            'view_item'             => __('View Vlog', 'vlog-plugin'),
            'all_items'             => __('All Vlogs', 'vlog-plugin'),
            'search_items'          => __('Search Vlogs', 'vlog-plugin'),
            'parent_item_colon'     => __('Parent Vlogs:', 'vlog-plugin'),
            'not_found'             => __('No vlogs found.', 'vlog-plugin'),
            'not_found_in_trash'    => __('No vlogs found in Trash.', 'vlog-plugin'),
            'featured_image'        => _x('Vlog Thumbnail', 'Overrides the "Featured Image" phrase', 'vlog-plugin'),
            'set_featured_image'    => _x('Set thumbnail', 'Overrides the "Set featured image" phrase', 'vlog-plugin'),
            'remove_featured_image' => _x('Remove thumbnail', 'Overrides the "Remove featured image" phrase', 'vlog-plugin'),
            'use_featured_image'    => _x('Use as thumbnail', 'Overrides the "Use as featured image" phrase', 'vlog-plugin'),
            'archives'              => _x('Vlog archives', 'The post type archive label', 'vlog-plugin'),
            'insert_into_item'      => _x('Insert into vlog', 'Overrides the "Insert into post" phrase', 'vlog-plugin'),
            'uploaded_to_this_item' => _x('Uploaded to this vlog', 'Overrides the "Uploaded to this post" phrase', 'vlog-plugin'),
            'filter_items_list'     => _x('Filter vlogs list', 'Screen reader text for the filter links', 'vlog-plugin'),
            'items_list_navigation' => _x('Vlogs list navigation', 'Screen reader text for the pagination', 'vlog-plugin'),
            'items_list'            => _x('Vlogs list', 'Screen reader text for the items list', 'vlog-plugin'),
        ];

        $args = [
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'show_in_rest'       => true,
            'query_var'          => true,
            'rewrite'            => ['slug' => 'vlog'],
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => 20,
            'menu_icon'          => 'dashicons-video-alt3',
            'supports'           => ['title', 'editor', 'thumbnail', 'excerpt', 'comments', 'custom-fields'],
            'taxonomies'         => ['vlog_category', 'vlog_tag'],
        ];

        register_post_type('vlog', $args);
    }

    /**
     * Register vlog category taxonomy
     */
    private function register_vlog_category_taxonomy(): void {
        $labels = [
            'name'                       => _x('Vlog Categories', 'Taxonomy General Name', 'vlog-plugin'),
            'singular_name'              => _x('Vlog Category', 'Taxonomy Singular Name', 'vlog-plugin'),
            'menu_name'                  => __('Categories', 'vlog-plugin'),
            'all_items'                  => __('All Categories', 'vlog-plugin'),
            'parent_item'                => __('Parent Category', 'vlog-plugin'),
            'parent_item_colon'          => __('Parent Category:', 'vlog-plugin'),
            'new_item_name'              => __('New Category Name', 'vlog-plugin'),
            'add_new_item'               => __('Add New Category', 'vlog-plugin'),
            'edit_item'                  => __('Edit Category', 'vlog-plugin'),
            'update_item'                => __('Update Category', 'vlog-plugin'),
            'view_item'                  => __('View Category', 'vlog-plugin'),
            'separate_items_with_commas' => __('Separate categories with commas', 'vlog-plugin'),
            'add_or_remove_items'        => __('Add or remove categories', 'vlog-plugin'),
            'choose_from_most_used'      => __('Choose from the most used', 'vlog-plugin'),
            'popular_items'              => __('Popular Categories', 'vlog-plugin'),
            'search_items'               => __('Search Categories', 'vlog-plugin'),
            'not_found'                  => __('Not Found', 'vlog-plugin'),
            'no_terms'                   => __('No categories', 'vlog-plugin'),
            'items_list'                 => __('Categories list', 'vlog-plugin'),
            'items_list_navigation'      => __('Categories list navigation', 'vlog-plugin'),
        ];

        $args = [
            'labels'                     => $labels,
            'hierarchical'               => true,
            'public'                     => true,
            'show_ui'                    => true,
            'show_admin_column'          => true,
            'show_in_nav_menus'          => true,
            'show_tagcloud'              => true,
            'show_in_rest'               => true,
            'rewrite'                    => ['slug' => 'vlog-category'],
        ];

        register_taxonomy('vlog_category', ['vlog'], $args);
    }

    /**
     * Register vlog tag taxonomy
     */
    private function register_vlog_tag_taxonomy(): void {
        $labels = [
            'name'                       => _x('Vlog Tags', 'Taxonomy General Name', 'vlog-plugin'),
            'singular_name'              => _x('Vlog Tag', 'Taxonomy Singular Name', 'vlog-plugin'),
            'menu_name'                  => __('Tags', 'vlog-plugin'),
            'all_items'                  => __('All Tags', 'vlog-plugin'),
            'new_item_name'              => __('New Tag Name', 'vlog-plugin'),
            'add_new_item'               => __('Add New Tag', 'vlog-plugin'),
            'edit_item'                  => __('Edit Tag', 'vlog-plugin'),
            'update_item'                => __('Update Tag', 'vlog-plugin'),
            'view_item'                  => __('View Tag', 'vlog-plugin'),
            'separate_items_with_commas' => __('Separate tags with commas', 'vlog-plugin'),
            'add_or_remove_items'        => __('Add or remove tags', 'vlog-plugin'),
            'choose_from_most_used'      => __('Choose from the most used', 'vlog-plugin'),
            'popular_items'              => __('Popular Tags', 'vlog-plugin'),
            'search_items'               => __('Search Tags', 'vlog-plugin'),
            'not_found'                  => __('Not Found', 'vlog-plugin'),
            'no_terms'                   => __('No tags', 'vlog-plugin'),
            'items_list'                 => __('Tags list', 'vlog-plugin'),
            'items_list_navigation'      => __('Tags list navigation', 'vlog-plugin'),
        ];

        $args = [
            'labels'                     => $labels,
            'hierarchical'               => false,
            'public'                     => true,
            'show_ui'                    => true,
            'show_admin_column'          => true,
            'show_in_nav_menus'          => true,
            'show_tagcloud'              => true,
            'show_in_rest'               => true,
            'rewrite'                    => ['slug' => 'vlog-tag'],
        ];

        register_taxonomy('vlog_tag', ['vlog'], $args);
    }
}
